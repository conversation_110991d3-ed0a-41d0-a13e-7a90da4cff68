# -*- coding: utf-8 -*-
"""
GUI文件操作模块
包含文件选择、保存等相关功能
"""

import os
from tkinter import filedialog, messagebox


class GUIFileOperationsMixin:
    """GUI文件操作Mixin类"""
    
    def select_excel_file(self):
        """选择Excel文件"""
        filetypes = [
            ("Excel files", "*.xlsx *.xls"),
            ("Excel 2007+", "*.xlsx"),
            ("Excel 97-2003", "*.xls"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=filetypes
        )
        
        if filename:
            self.excel_path_var.set(filename)
            self.current_excel_path = filename
            self.logger.info(f"已选择Excel文件: {filename}")
    
    def save_results(self):
        """保存结果"""
        # 检查是否有完整的分析结果
        if hasattr(self, 'all_analysis_results') and self.all_analysis_results:
            # 有完整的分析结果，保存所有结果（包括失败的）
            self._save_all_results()
        elif self.data_processor.filtered_stocks:
            # 只有筛选后的结果，使用旧的保存方式
            self._save_filtered_results()
        else:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
    
    def _save_all_results(self):
        """保存所有分析结果（包括成功和失败的）"""
        filetypes = [
            ("Excel files", "*.xlsx"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.asksaveasfilename(
            title="保存完整分析结果（包括失败的股票）",
            defaultextension=".xlsx",
            filetypes=filetypes
        )
        
        if filename:
            try:
                success = self.data_processor.save_all_results_to_excel(filename, self.all_analysis_results)
                if success:
                    # 统计结果
                    total_count = len(self.all_analysis_results)
                    success_count = len([r for r in self.all_analysis_results if r.get('status') == '分析成功'])
                    failed_count = total_count - success_count
                    filtered_count = len([r for r in self.all_analysis_results 
                                        if (r.get('status') == '分析成功' and 
                                            r.get('today_fund', 0) > r.get('yesterday_fund', 0) > r.get('day_before_yesterday_fund', 0))])
                    
                    info_msg = f"""保存成功！
                    
文件位置: {filename}

分析统计:
• 总计股票: {total_count} 只
• 分析成功: {success_count} 只
• 分析失败: {failed_count} 只
• 符合筛选条件: {filtered_count} 只

说明: 此文件包含所有股票的完整分析结果"""
                    
                    messagebox.showinfo("保存成功", info_msg)
                    self.logger.info(f"完整分析结果已保存到: {filename}")
                else:
                    messagebox.showerror("错误", "保存失败")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
                self.logger.error(f"保存失败: {str(e)}")
    
    def _save_filtered_results(self):
        """保存筛选后的结果（仅符合条件的股票）"""
        filetypes = [
            ("Excel files", "*.xlsx"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.asksaveasfilename(
            title="保存筛选结果（仅符合条件的股票）",
            defaultextension=".xlsx",
            filetypes=filetypes
        )
        
        if filename:
            try:
                success = self.data_processor.save_results_to_excel(filename, self.data_processor.filtered_stocks)
                if success:
                    messagebox.showinfo("成功", f"结果已保存到: {filename}")
                    self.logger.info(f"结果已保存到: {filename}")
                else:
                    messagebox.showerror("错误", "保存失败")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
                self.logger.error(f"保存失败: {str(e)}")