# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
Always respond in Chinese-simplified.

## Project Overview

This is a Chinese stock screening tool that automates data extraction from Compass (指南针) financial software using OCR and GUI automation. The application extracts fund flow data for multiple stocks and filters them based on configurable criteria.

## Key Architecture Components

### Core Modules
- **main.py**: Entry point that delegates to gui_main.py 
- **gui_main.py**: Main Tkinter application with modular design using mixin classes
- **config.py**: Central configuration with software paths, OCR settings, and filtering logic
- **compass_automator.py**: Windows automation for Compass software interaction using pywinauto
- **data_processor.py**: Excel file processing and stock filtering logic with pandas
- **compass_data_extractor.py**: Core data extraction logic combining keyboard automation and OCR

### GUI Modules (Mixin Pattern)
- **gui_setup.py**: GUI layout and widget initialization
- **gui_file_operations.py**: Excel file handling and data import/export
- **gui_ocr_operations.py**: OCR region selection and configuration
- **gui_mouse_operations.py**: Mouse position management and operations
- **gui_analysis.py**: Stock analysis workflow and automation
- **gui_display.py**: Results display and data visualization
- **gui_handlers.py**: Event handling and user interactions
- **gui_buy_signal_operations.py**: Buy signal GUI operations (wrapper for unified operations)
- **gui_sell_signal_operations.py**: Sell signal GUI operations (wrapper for unified operations)  
- **gui_signal_operations.py**: Unified GUI operations for both buy and sell signals
- **gui_stock_import_operations.py**: Stock code import and processing operations
- **gui_web_automation_operations.py**: Web automation and data extraction operations

### OCR System (Multi-Engine)
- **simple_ocr_manager.py**: Simplified OCR manager (recommended)
- **ocr_manager_optimized.py**: Complex multi-engine OCR manager
- **ocr_strategy_optimizer.py**: Dynamic OCR strategy selection and optimization
- **paddleocr_compatibility_wrapper.py**: PaddleOCR compatibility wrapper
- **simple_paddleocr_engine.py**: Simplified PaddleOCR engine implementation
- **image_processor.py**: Image preprocessing and enhancement for OCR accuracy

### Specialized Components
- **region_selector.py**: Interactive screen region selection for OCR
- **smart_waiter.py**: Intelligent waiting strategies for GUI automation
- **signal_analyzer.py**: Unified signal detection logic for both buy and sell signals
- **unified_signal_monitor.py**: Core unified signal monitoring with configurable behavior
- **base_signal_monitor.py**: Base class for signal monitoring functionality
- **buy_signal_monitor.py**: Buy signal monitoring (wrapper for unified monitor)
- **sell_signal_monitor.py**: Sell signal monitoring (wrapper for unified monitor)
- **dingtalk_notifier.py**: DingTalk integration for notifications
- **web_automator.py**: Web automation using Playwright for stock data extraction
- **html_data_parser.py**: HTML parsing and data extraction from web sources
- **credentials_manager.py**: Secure credential management for web automation
- **data_config_wizard.py**: Interactive configuration wizard for data extraction
- **web_element_config_wizard.py**: Web element configuration wizard
- **web_element_selector.py**: Web element selection utilities
- **version_utils.py**: Version management and update utilities
- **update.py**: Application update and migration handler
- **create_deploy_package.py**: Deployment package creation tool
- **install_dependencies.py**: Smart dependency installer with error handling

## Development Commands

### Installation
```bash
pip install -r requirements.txt
# 或者使用智能安装器（推荐）
python install_dependencies.py
# 首次安装需要安装Playwright浏览器
playwright install chromium
```

### Running the Application
```bash
python main.py
# 或者在Windows使用批处理脚本（推荐）
start.bat
```

### Deployment
```bash
# 创建部署包
python create_deploy_package.py
# 或者使用一键部署脚本（推荐）
deploy.bat
```

### Testing OCR Components
```bash
python test_stock_import.py             # 股票导入功能测试
python test_web_automation.py           # 网页自动化测试
python test_complete_functionality.py   # 完整功能测试
python test_stock_import_functionality.py # 股票导入功能完整测试
python test_html_parser.py              # HTML解析器测试
python test_code_analysis.py            # 代码分析测试
python test_virtual_scroll_fix.py       # 虚拟滚动修复测试
```

## Configuration Management

### Main Configuration (`config.py`)
- **COMPASS_SOFTWARE**: Compass software paths, window detection settings, and OCR regions
- **APP_CONFIG**: Application behavior, OCR settings, and processing parameters  
- **GUI_CONFIG**: UI layout and appearance settings
- **FILTER_CONFIG**: Stock filtering criteria and validation rules

### OCR Engine Selection
The system supports two OCR engine modes in `APP_CONFIG['ocr_settings']['engine_mode']`:
- **'simple_paddleocr'**: Simplified PaddleOCR engine (recommended, default)
- **'complex'**: Multi-engine system with EasyOCR and PaddleOCR fallback

### Dynamic Configuration
- OCR region coordinates are automatically saved to `config.py` when selected via GUI
- The `update_ocr_region_config()` function handles real-time configuration updates

## Key Design Patterns

### Mixin Pattern for GUI
The GUI system uses multiple mixin classes to organize functionality:
- Each mixin handles a specific aspect (file ops, OCR, analysis, etc.)
- Main `StockScreenerGUI` class inherits from all mixins
- Enables modular development and better code organization

### OCR Strategy Pattern
The system employs multiple OCR strategies that are dynamically selected based on:
- Image quality assessment
- Historical performance data
- Content type (text vs percentages)

### Manager Pattern
- **OCRManager**: Global singleton managing multiple OCR engines
- **DataExtractor**: Orchestrates keyboard automation and OCR operations
- **SmartWaiter**: Adaptive timing for GUI operations

### Multi-Engine Fallback
OCR operations use a fallback hierarchy:
1. **Simple Mode (default)**: PaddleOCR with optimized preprocessing
2. **Complex Mode**: EasyOCR with GPU acceleration, PaddleOCR as backup
3. Different preprocessing strategies for each engine based on content type

### Signal Monitoring Architecture
The system uses a unified signal monitoring approach:
- **BaseSignalMonitor**: Abstract base class defining signal monitoring interface
- **UnifiedSignalMonitor**: Core implementation handling both buy and sell signals
- **BuySignalMonitor/SellSignalMonitor**: Specialized wrappers with signal-specific configuration
- Configurable behavior through `APP_CONFIG` settings for each signal type

## Signal Detection System

The application includes a comprehensive signal detection system:
- **Real-time monitoring**: Continuous scanning for buy and sell signals in stock data
- **Unified monitoring**: Single codebase handling both buy and sell signal types
- **DingTalk notifications**: Automatic notifications when signals are detected
- **State tracking**: Monitors position changes and status updates
- **GUI integration**: Signal operations are integrated into the main interface
- **Web-based data extraction**: Alternative data source through web automation
- **Configurable thresholds**: Customizable signal detection criteria per signal type

## Important Development Notes

### Windows-Specific Dependencies
- This application is designed for Windows and requires Compass financial software
- Uses pywinauto for Windows GUI automation
- Screen capture functionality depends on Windows APIs
- Playwright web automation supports cross-platform operation

### OCR Requirements
- First-time PaddleOCR initialization downloads ML models
- EasyOCR (complex mode) downloads large ML models (~100MB+) and requires GPU for optimal performance
- GPU acceleration requires CUDA-compatible hardware
- Debug images are automatically saved to assist with OCR troubleshooting
- Simple PaddleOCR mode is recommended for most use cases

### Threading Architecture
- GUI operations run on main thread
- OCR processing and automation occur in background threads
- Message queue system handles thread communication

### Error Handling Strategy
- OCR operations have built-in retry mechanisms with strategy switching
- Comprehensive error logging and user notification systems
- Graceful degradation when OCR engines fail

## Deployment and Distribution

### Automated Deployment
- `create_deploy_package.py`: Creates portable deployment packages
- `deploy.bat`: Windows batch script for one-click deployment
- Handles dependency installation and configuration setup

### Version Management
- `version.json` and `version.txt`: Track application versions
- `version_utils.py`: Handles application updates and version comparisons
- `update.py`: Handles application updates and migrations
- Version format: vYYYY.MM.DD.HHMM for deployment packages

## File Exclusions and Data Management

### Git Repository Structure
The project uses a comprehensive .gitignore that excludes:
- All test files (`test_*.py`) - these are present in working directory but not tracked
- OCR debug images and temporary screenshots
- User data files (Excel, logs, configurations)
- Development tools and IDE configurations
- Web credentials and selector configuration files (`web_credentials.json`, `web_element_selectors.json`)

This keeps the repository focused on core business logic while enabling local development and debugging.

### Important Configuration Files
- **`web_credentials.json`**: Stores encrypted web automation credentials
- **`web_element_selectors.json`**: Contains web element selector configurations for different sites
- **`config.py`**: Main configuration file that gets updated dynamically during runtime

## Code Organization Guidelines

### File Size Management
如果文件超过500行，则按模块或者功能拆分成小文件。当进行代码重构时，应该：
- 将相关功能分组到独立的模块中
- 保持单一职责原则，每个文件专注于特定功能
- 使用清晰的模块命名约定
- 通过导入语句维护模块间的依赖关系

### Mixin Organization
GUI模块使用mixin模式组织，每个mixin文件应该：
- 专注于单一功能领域（如文件操作、OCR操作等）
- 保持方法的独立性，避免过度耦合
- 使用清晰的命名约定（如 `GUIFileOperationsMixin`）
- 在主类中按逻辑顺序继承mixin类

## 项目状态和注意事项

### 当前开发状态
- 项目处于活跃开发状态，当前分支：`ocrokv1`
- 主要功能已实现，包括OCR数据提取、信号监控、Web自动化
- 推荐使用简化的PaddleOCR引擎以获得最佳性能和稳定性
- Web自动化功能需要初始配置来设置目标网站的元素选择器

### 测试文件说明
项目包含多个测试文件（`test_*.py`），但这些文件：
- 不在Git版本控制中追踪
- 主要用于开发过程中的功能验证
- 包含了各个模块的独立测试功能
- 可以用于调试和验证特定功能组件