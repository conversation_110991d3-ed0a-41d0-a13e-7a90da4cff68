# -*- coding: utf-8 -*-
"""
股票筛选器智能更新脚本
支持增量更新、配置保护、备份回滚和多种更新源
"""

import os
import sys
import json
import shutil
import hashlib
import zipfile
import tempfile
import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import subprocess

class SmartUpdater:
    def __init__(self, current_dir=None):
        """
        初始化智能更新器
        
        Args:
            current_dir: 当前程序目录，默认为脚本所在目录
        """
        self.current_dir = Path(current_dir) if current_dir else Path(".")
        self.backup_dir = self.current_dir / "backup"
        self.config_backup_dir = self.current_dir / "config_backup"
        self.temp_dir = None
        
        # 受保护的配置文件（不会被更新覆盖）
        self.protected_files = [
            "config_backup/config.py",
            "version.txt",
            "version.json",
            "installation_log.json",
            "update_log.json",
        ]
        
        # 用户自定义的配置文件模式
        self.user_config_patterns = [
            "*_config.py",
            "user_*.py",
            "custom_*.py",
        ]
        
        self.current_version = self.load_current_version()
        self.update_log = []
        
        print("=" * 60)
        print("股票筛选器智能更新器")
        print("=" * 60)
        print(f"当前目录: {self.current_dir.absolute()}")
        print(f"当前版本: {self.current_version.get('version', '未知')}")
        print("=" * 60)
    
    def load_current_version(self) -> Dict:
        """加载当前版本信息"""
        version_file = self.current_dir / "version.json"
        if version_file.exists():
            try:
                with open(version_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️  读取版本信息失败: {e}")
        
        return {
            'version': 'v0.0.0.0000',
            'build_timestamp': 0,
            'description': '未知版本'
        }
    
    def load_update_version(self, update_source) -> Optional[Dict]:
        """从更新源加载版本信息"""
        if isinstance(update_source, (str, Path)):
            update_path = Path(update_source)
            
            # 如果是目录，查找version.json
            if update_path.is_dir():
                version_file = update_path / "version.json"
            # 如果是ZIP文件，解压后查找
            elif update_path.suffix.lower() == '.zip':
                return self.load_version_from_zip(update_path)
            else:
                version_file = update_path
            
            if version_file.exists():
                try:
                    with open(version_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except Exception as e:
                    print(f"⚠️  读取更新版本信息失败: {e}")
        
        return None
    
    def load_version_from_zip(self, zip_path: Path) -> Optional[Dict]:
        """从ZIP文件中加载版本信息"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                # 查找version.json文件
                for file_info in zipf.filelist:
                    if file_info.filename.endswith('version.json'):
                        with zipf.open(file_info) as f:
                            return json.loads(f.read().decode('utf-8'))
        except Exception as e:
            print(f"⚠️  从ZIP文件读取版本信息失败: {e}")
        
        return None
    
    def compare_versions(self, current_version: Dict, new_version: Dict) -> str:
        """比较版本"""
        current_timestamp = current_version.get('build_timestamp', 0)
        new_timestamp = new_version.get('build_timestamp', 0)
        
        if new_timestamp > current_timestamp:
            return 'newer'
        elif new_timestamp < current_timestamp:
            return 'older'
        else:
            return 'same'
    
    def is_protected_file(self, file_path: Path) -> bool:
        """检查文件是否受保护"""
        file_str = str(file_path)
        
        # 检查明确的受保护文件
        for protected in self.protected_files:
            if file_str.endswith(protected) or protected in file_str:
                return True
        
        # 检查用户配置模式
        for pattern in self.user_config_patterns:
            if pattern.startswith('*') and pattern.endswith('*'):
                if pattern[1:-1] in file_path.name:
                    return True
            elif pattern.startswith('*'):
                if file_path.name.endswith(pattern[1:]):
                    return True
            elif pattern.endswith('*'):
                if file_path.name.startswith(pattern[:-1]):
                    return True
        
        return False
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def backup_current_version(self) -> Path:
        """备份当前版本"""
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = self.backup_dir / f"backup_{timestamp}"
        
        print(f"🔄 正在备份当前版本到: {backup_path}")
        
        try:
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # 备份src目录
            src_dir = self.current_dir / "src"
            if src_dir.exists():
                shutil.copytree(src_dir, backup_path / "src")
            
            # 备份关键文件
            key_files = ["version.json", "version.txt", "requirements.txt"]
            for key_file in key_files:
                file_path = self.current_dir / key_file
                if file_path.exists():
                    shutil.copy2(file_path, backup_path / key_file)
            
            print(f"✅ 备份完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            raise
    
    def backup_user_configs(self) -> Dict[str, str]:
        """备份用户配置文件"""
        print("🔒 正在备份用户配置...")
        
        config_backups = {}
        
        # 备份config.py中的OCR区域配置
        config_file = self.current_dir / "src" / "config.py"
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 提取OCR区域配置
                ocr_region_pattern = r"'ocr_region':\s*\{[^}]+\}"
                import re
                match = re.search(ocr_region_pattern, content)
                if match:
                    config_backups['ocr_region'] = match.group()
                    
                # 备份整个配置文件到备份目录
                backup_config_path = self.config_backup_dir / f"config_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
                self.config_backup_dir.mkdir(parents=True, exist_ok=True)
                shutil.copy2(config_file, backup_config_path)
                
                print(f"✅ 配置文件已备份到: {backup_config_path}")
                
            except Exception as e:
                print(f"⚠️  配置备份失败: {e}")
        
        return config_backups
    
    def restore_user_configs(self, config_backups: Dict[str, str]):
        """恢复用户配置"""
        if not config_backups:
            return
        
        print("🔄 正在恢复用户配置...")
        
        config_file = self.current_dir / "src" / "config.py"
        if config_file.exists() and 'ocr_region' in config_backups:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换OCR区域配置
                ocr_region_pattern = r"'ocr_region':\s*\{[^}]+\}"
                new_content = re.sub(ocr_region_pattern, config_backups['ocr_region'], content)
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ 用户配置恢复完成")
                
            except Exception as e:
                print(f"⚠️  配置恢复失败: {e}")
    
    def extract_update_package(self, update_source: Path) -> Path:
        """解压更新包"""
        if update_source.is_dir():
            return update_source
        
        if update_source.suffix.lower() == '.zip':
            self.temp_dir = Path(tempfile.mkdtemp(prefix="stock_update_"))
            print(f"📦 正在解压更新包到: {self.temp_dir}")
            
            try:
                with zipfile.ZipFile(update_source, 'r') as zipf:
                    zipf.extractall(self.temp_dir)
                
                # 查找实际的更新目录（可能在子目录中）
                for item in self.temp_dir.iterdir():
                    if item.is_dir() and (item / "src").exists():
                        return item
                
                return self.temp_dir
                
            except Exception as e:
                print(f"❌ 解压更新包失败: {e}")
                raise
        
        raise ValueError(f"不支持的更新包格式: {update_source}")
    
    def get_update_files(self, update_dir: Path) -> List[Tuple[Path, Path]]:
        """获取需要更新的文件列表"""
        update_files = []
        
        # 更新src目录中的文件
        src_dir = update_dir / "src"
        if src_dir.exists():
            for root, dirs, files in os.walk(src_dir):
                for file in files:
                    source_file = Path(root) / file
                    rel_path = source_file.relative_to(update_dir)
                    target_file = self.current_dir / rel_path
                    
                    if not self.is_protected_file(rel_path):
                        update_files.append((source_file, target_file))
        
        # 更新根目录的关键文件
        key_files = ["requirements.txt", "version.json", "version.txt"]
        for key_file in key_files:
            source_file = update_dir / key_file
            if source_file.exists():
                target_file = self.current_dir / key_file
                update_files.append((source_file, target_file))
        
        return update_files
    
    def apply_update(self, update_files: List[Tuple[Path, Path]]) -> bool:
        """应用更新"""
        print(f"🚀 开始应用更新，共 {len(update_files)} 个文件...")
        
        success_count = 0
        failed_files = []
        
        for source_file, target_file in update_files:
            try:
                # 确保目标目录存在
                target_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                shutil.copy2(source_file, target_file)
                success_count += 1
                
                rel_path = target_file.relative_to(self.current_dir)
                print(f"  ✅ 已更新: {rel_path}")
                
                self.update_log.append({
                    'action': 'update',
                    'file': str(rel_path),
                    'status': 'success'
                })
                
            except Exception as e:
                failed_files.append((source_file, target_file, str(e)))
                print(f"  ❌ 更新失败: {target_file.name} - {e}")
                
                self.update_log.append({
                    'action': 'update',
                    'file': str(target_file.relative_to(self.current_dir)),
                    'status': 'failed',
                    'error': str(e)
                })
        
        print(f"\n📊 更新统计:")
        print(f"   ✅ 成功: {success_count} 个文件")
        print(f"   ❌ 失败: {len(failed_files)} 个文件")
        
        return len(failed_files) == 0
    
    def update_dependencies(self) -> bool:
        """更新依赖包"""
        print("📦 检查依赖包更新...")
        
        install_script = self.current_dir / "install_dependencies.py"
        if not install_script.exists():
            print("⚠️  未找到依赖安装脚本，跳过依赖更新")
            return True
        
        try:
            # 运行依赖安装脚本
            result = subprocess.run(
                [sys.executable, str(install_script)],
                cwd=self.current_dir,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                print("✅ 依赖包更新完成")
                return True
            else:
                print(f"⚠️  依赖包更新失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 依赖包更新出错: {e}")
            return False
    
    def save_update_log(self):
        """保存更新日志"""
        log_file = self.current_dir / "update_log.json"
        
        log_data = {
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'previous_version': self.current_version,
            'update_log': self.update_log
        }
        
        try:
            # 读取现有日志
            existing_logs = []
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    existing_logs = json.load(f)
            
            # 添加新日志
            existing_logs.append(log_data)
            
            # 保持最近10次更新记录
            if len(existing_logs) > 10:
                existing_logs = existing_logs[-10:]
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(existing_logs, f, ensure_ascii=False, indent=2)
            
            print(f"📝 更新日志已保存到: {log_file}")
            
        except Exception as e:
            print(f"⚠️  保存更新日志失败: {e}")
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
                print("🧹 临时文件清理完成")
            except Exception as e:
                print(f"⚠️  临时文件清理失败: {e}")
    
    def rollback_update(self, backup_path: Path) -> bool:
        """回滚更新"""
        print(f"🔄 正在回滚到备份版本: {backup_path}")
        
        try:
            # 恢复src目录
            current_src = self.current_dir / "src"
            backup_src = backup_path / "src"
            
            if backup_src.exists():
                if current_src.exists():
                    shutil.rmtree(current_src)
                shutil.copytree(backup_src, current_src)
            
            # 恢复关键文件
            key_files = ["version.json", "version.txt", "requirements.txt"]
            for key_file in key_files:
                backup_file = backup_path / key_file
                current_file = self.current_dir / key_file
                
                if backup_file.exists():
                    shutil.copy2(backup_file, current_file)
            
            print("✅ 回滚完成")
            return True
            
        except Exception as e:
            print(f"❌ 回滚失败: {e}")
            return False
    
    def perform_update(self, update_source: str) -> bool:
        """执行完整的更新流程"""
        update_path = Path(update_source)
        
        if not update_path.exists():
            print(f"❌ 更新源不存在: {update_source}")
            return False
        
        backup_path = None
        config_backups = {}
        
        try:
            # 1. 加载更新版本信息
            print("📋 检查更新版本...")
            new_version = self.load_update_version(update_path)
            
            if not new_version:
                print("❌ 无法读取更新版本信息")
                return False
            
            print(f"📦 更新版本: {new_version.get('version', '未知')}")
            print(f"📅 构建时间: {new_version.get('build_time', '未知')}")
            
            # 2. 比较版本
            version_comparison = self.compare_versions(self.current_version, new_version)
            
            if version_comparison == 'same':
                print("ℹ️  版本相同，无需更新")
                return True
            elif version_comparison == 'older':
                print("⚠️  更新版本较旧，是否继续？")
                response = input("请输入 Y 继续，其他键取消: ").strip().lower()
                if response not in ['y', 'yes', '是']:
                    print("更新已取消")
                    return False
            
            # 3. 显示更新内容
            changes = new_version.get('changes', [])
            if changes:
                print(f"\n📋 更新内容:")
                for change in changes:
                    print(f"   • {change}")
            
            print(f"\n是否继续更新？ (Y/N): ", end='')
            response = input().strip().lower()
            if response not in ['y', 'yes', '是']:
                print("更新已取消")
                return False
            
            # 4. 备份当前版本
            backup_path = self.backup_current_version()
            
            # 5. 备份用户配置
            config_backups = self.backup_user_configs()
            
            # 6. 解压更新包
            update_dir = self.extract_update_package(update_path)
            
            # 7. 获取更新文件列表
            update_files = self.get_update_files(update_dir)
            
            if not update_files:
                print("⚠️  没有找到需要更新的文件")
                return False
            
            print(f"📦 准备更新 {len(update_files)} 个文件")
            
            # 8. 应用更新
            update_success = self.apply_update(update_files)
            
            # 9. 恢复用户配置
            self.restore_user_configs(config_backups)
            
            # 10. 更新依赖
            if update_success:
                deps_success = self.update_dependencies()
                if not deps_success:
                    print("⚠️  依赖更新失败，但文件更新成功")
            
            # 11. 保存更新日志
            self.save_update_log()
            
            if update_success:
                print(f"\n🎉 更新完成！")
                print(f"   版本: {self.current_version.get('version')} → {new_version.get('version')}")
                print(f"   备份: {backup_path}")
                return True
            else:
                print(f"\n❌ 更新失败，尝试回滚...")
                if backup_path:
                    self.rollback_update(backup_path)
                return False
                
        except Exception as e:
            print(f"\n❌ 更新过程出错: {e}")
            
            if backup_path:
                print("尝试回滚到备份版本...")
                self.rollback_update(backup_path)
            
            return False
            
        finally:
            # 清理临时文件
            self.cleanup_temp_files()

def main():
    """主函数"""
    print("使用方法:")
    print("  python update.py <更新源>")
    print("  更新源可以是:")
    print("    - 本地目录路径")
    print("    - ZIP文件路径")
    print("    - 网络URL（未来支持）")
    print()
    
    if len(sys.argv) < 2:
        print("请指定更新源")
        update_source = input("请输入更新源路径: ").strip()
        if not update_source:
            print("未指定更新源，退出")
            return
    else:
        update_source = sys.argv[1]
    
    updater = SmartUpdater()
    
    try:
        success = updater.perform_update(update_source)
        
        if success:
            print("\n✅ 更新成功完成！")
            print("建议重启程序以应用所有更改")
        else:
            print("\n❌ 更新失败")
            print("请检查错误信息或使用备份恢复")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  更新被用户中断")
    except Exception as e:
        print(f"\n❌ 更新过程出现错误: {e}")
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()