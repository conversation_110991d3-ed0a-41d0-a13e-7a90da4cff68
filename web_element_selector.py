# -*- coding: utf-8 -*-
"""
网页元素选择工具
提供交互式的网页元素选择和选择器自动生成功能
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Callable, Tuple
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from config import WEB_AUTOMATION_CONFIG


class WebElementSelector:
    """网页元素选择器"""
    
    def __init__(self, callback: Optional[Callable] = None):
        """
        初始化网页元素选择器
        
        Args:
            callback: 选择完成后的回调函数，接收选择器配置字典
        """
        self.logger = logging.getLogger(__name__)
        self.callback = callback
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playlist = None
        
        # 配置步骤
        self.config_steps = [
            {
                'name': 'username_input',
                'description': '请点击用户名输入框',
                'hint': '找到页面上的用户名输入框并点击'
            },
            {
                'name': 'password_input', 
                'description': '请点击密码输入框',
                'hint': '找到页面上的密码输入框并点击'
            },
            {
                'name': 'data_table',
                'description': '请点击包含股票数据的表格',
                'hint': '找到包含"小草竞王"等数据的表格并点击'
            },
            {
                'name': 'stock_code_column',
                'description': '请点击股票代码列',
                'hint': '在数据表格中点击股票代码列的任意一个单元格'
            },
            {
                'name': 'stock_name_column',
                'description': '请点击股票名称列',
                'hint': '在数据表格中点击股票名称列的任意一个单元格'
            },
            {
                'name': 'xiaocao_jingwang_column',
                'description': '请点击"小草竞王"列',
                'hint': '在数据表格中点击"小草竞王"列的任意一个单元格'
            },
            {
                'name': 'xiaocao_hongpan_column',
                'description': '请点击"小草红盘起爆"列', 
                'hint': '在数据表格中点击"小草红盘起爆"列的任意一个单元格'
            },
            {
                'name': 'xiaocao_lvpan_column',
                'description': '请点击"小草绿盘低吸"列',
                'hint': '在数据表格中点击"小草绿盘低吸"列的任意一个单元格'
            },
            {
                'name': 'xiaocao_lianban_column',
                'description': '请点击"小草连板接力"列',
                'hint': '在数据表格中点击"小草连板接力"列的任意一个单元格'
            }
        ]
        
        self.current_step = 0
        self.selected_elements = {}
        self.is_selecting = False
        
    async def start_selection(self, target_url: str = None) -> Optional[Dict]:
        """
        开始元素选择流程
        
        Args:
            target_url: 目标网站URL，如果为None则使用配置中的URL
            
        Returns:
            选择器配置字典或None
        """
        try:
            if target_url is None:
                target_url = WEB_AUTOMATION_CONFIG['target_url']
            
            # 初始化浏览器
            await self._initialize_browser()
            
            # 导航到目标页面
            await self.page.goto(target_url)
            await self.page.wait_for_load_state('networkidle')
            
            # 注入高亮脚本
            await self._inject_highlight_script()
            
            # 开始逐步选择流程
            await self._start_step_by_step_selection()
            
            return self.selected_elements
            
        except Exception as e:
            self.logger.error(f"元素选择失败: {str(e)}")
            return None
        finally:
            await self._cleanup()
    
    async def _initialize_browser(self):
        """初始化浏览器"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=False,
            timeout=30000
        )
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await self.context.new_page()
        
    async def _inject_highlight_script(self):
        """注入元素高亮脚本"""
        highlight_script = """
        (function() {
            window.currentHighlighted = null;
            window.selectedElement = null;
            window.elementSelectCallback = null;
            
            // 高亮元素
            function highlightElement(element) {
                if (window.currentHighlighted) {
                    removeHighlight(window.currentHighlighted);
                }
                element.style.outline = '3px solid red';
                element.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
                element.style.cursor = 'pointer';
                window.currentHighlighted = element;
            }
            
            // 移除高亮
            function removeHighlight(element) {
                element.style.outline = '';
                element.style.backgroundColor = '';
                element.style.cursor = '';
            }
            
            // 生成元素选择器
            function generateSelectors(element) {
                const selectors = [];
                const tagName = element.tagName.toLowerCase();
                
                // ID选择器 - 最高优先级
                if (element.id) {
                    selectors.push({
                        type: 'id',
                        selector: '#' + element.id,
                        priority: 1
                    });
                }
                
                // 对于input元素，优先使用属性组合选择器
                if (tagName === 'input') {
                    // Type + Name 组合选择器 - 最精确
                    if (element.type && element.name) {
                        selectors.push({
                            type: 'type_name_combo',
                            selector: `input[type="${element.type}"][name="${element.name}"]`,
                            priority: 1.2
                        });
                    }
                    
                    // Type + Placeholder 组合选择器
                    if (element.type && element.placeholder) {
                        selectors.push({
                            type: 'type_placeholder_combo',
                            selector: `input[type="${element.type}"][placeholder*="${element.placeholder.substring(0, 10)}"]`,
                            priority: 1.3
                        });
                    }
                    
                    // Name属性选择器 - 对input元素高优先级
                    if (element.name) {
                        selectors.push({
                            type: 'name',
                            selector: `input[name="${element.name}"]`,
                            priority: 1.4
                        });
                    }
                    
                    // Type属性选择器 - 对input元素高优先级
                    if (element.type) {
                        selectors.push({
                            type: 'type',
                            selector: `input[type="${element.type}"]`,
                            priority: 1.5
                        });
                    }
                    
                    // Placeholder属性选择器
                    if (element.placeholder) {
                        selectors.push({
                            type: 'placeholder',
                            selector: `input[placeholder*="${element.placeholder.substring(0, 15)}"]`,
                            priority: 1.6
                        });
                    }
                } else {
                    // 非input元素的属性选择器
                    if (element.name) {
                        selectors.push({
                            type: 'name',
                            selector: `${tagName}[name="${element.name}"]`,
                            priority: 1.8
                        });
                    }
                    
                    if (element.type) {
                        selectors.push({
                            type: 'type',
                            selector: `${tagName}[type="${element.type}"]`,
                            priority: 1.9
                        });
                    }
                }
                
                // Class选择器 - 降低优先级
                if (element.className && typeof element.className === 'string') {
                    const classes = element.className.trim().split(/\\s+/).filter(c => c);
                    if (classes.length > 0) {
                        // 先尝试较少的类名组合
                        if (classes.length <= 2) {
                            selectors.push({
                                type: 'class_simple',
                                selector: '.' + classes.join('.'),
                                priority: 2.5
                            });
                        }
                        
                        // 完整的类名组合
                        selectors.push({
                            type: 'class',
                            selector: '.' + classes.join('.'),
                            priority: 3
                        });
                    }
                }
                
                // 文本内容选择器（适用于按钮等）
                if (element.textContent && element.textContent.trim()) {
                    const text = element.textContent.trim();
                    if (text.length < 50 && text.length > 2) {  // 避免过长或过短的文本
                        selectors.push({
                            type: 'text',
                            selector: `//*[contains(text(), "${text}")]`,
                            priority: 3.5,
                            xpath: true
                        });
                    }
                }
                
                // 添加上下文选择器（父元素+当前元素）
                if (element.parentElement) {
                    const parent = element.parentElement;
                    if (parent.className || parent.id) {
                        let parentSelector = '';
                        if (parent.id) {
                            parentSelector = '#' + parent.id;
                        } else if (parent.className) {
                            const parentClasses = parent.className.trim().split(/\\s+/).filter(c => c);
                            if (parentClasses.length > 0 && parentClasses.length <= 3) {
                                parentSelector = '.' + parentClasses.join('.');
                            }
                        }
                        
                        if (parentSelector) {
                            // 父元素 + 标签名
                            selectors.push({
                                type: 'parent_context',
                                selector: `${parentSelector} ${tagName}`,
                                priority: 4
                            });
                            
                            // 父元素 + 标签名 + type（对input有效）
                            if (tagName === 'input' && element.type) {
                                selectors.push({
                                    type: 'parent_type_context',
                                    selector: `${parentSelector} input[type="${element.type}"]`,
                                    priority: 3.8
                                });
                            }
                        }
                    }
                }
                
                // CSS选择器路径 - 最后备选
                const cssPath = getCSSPath(element);
                if (cssPath) {
                    selectors.push({
                        type: 'css_path',
                        selector: cssPath,
                        priority: 5
                    });
                }
                
                return selectors.sort((a, b) => a.priority - b.priority);
            }
            
            // 生成CSS路径
            function getCSSPath(element) {
                if (element === document.body) return 'body';
                
                const names = [];
                while (element && element.nodeType === Node.ELEMENT_NODE) {
                    let selector = element.nodeName.toLowerCase();
                    
                    if (element.id) {
                        selector += '#' + element.id;
                        names.unshift(selector);
                        break;
                    } else {
                        let sibling = element;
                        let nth = 1;
                        while ((sibling = sibling.previousElementSibling)) {
                            if (sibling.nodeName.toLowerCase() === selector) nth++;
                        }
                        if (nth !== 1) selector += ':nth-child(' + nth + ')';
                    }
                    
                    names.unshift(selector);
                    element = element.parentNode;
                }
                
                return names.join(' > ');
            }
            
            // 鼠标悬停事件
            document.addEventListener('mouseover', function(e) {
                if (!window.isSelecting) return;
                e.preventDefault();
                e.stopPropagation();
                highlightElement(e.target);
            });
            
            // 鼠标点击事件
            document.addEventListener('click', function(e) {
                if (!window.isSelecting) return;
                e.preventDefault();
                e.stopPropagation();
                
                const element = e.target;
                const selectors = generateSelectors(element);
                
                // 设置选中的元素信息
                window.selectedElement = {
                    tagName: element.tagName,
                    id: element.id || '',
                    className: element.className || '',
                    name: element.name || '',
                    type: element.type || '',
                    placeholder: element.placeholder || '',
                    textContent: element.textContent ? element.textContent.trim().substring(0, 100) : '',
                    selectors: selectors,
                    // 添加父元素信息用于上下文识别
                    parentInfo: element.parentElement ? {
                        tagName: element.parentElement.tagName,
                        id: element.parentElement.id || '',
                        className: element.parentElement.className || ''
                    } : null
                };
                
                // 停止选择模式
                window.isSelecting = false;
                
                // 移除高亮
                if (window.currentHighlighted) {
                    removeHighlight(window.currentHighlighted);
                }
                
                // 调用回调函数
                if (window.elementSelectCallback) {
                    window.elementSelectCallback(window.selectedElement);
                }
            });
            
            // 启用选择模式
            window.startSelection = function(callback) {
                window.isSelecting = true;
                window.elementSelectCallback = callback;
                document.body.style.cursor = 'crosshair';
            };
            
            // 停止选择模式
            window.stopSelection = function() {
                window.isSelecting = false;
                window.elementSelectCallback = null;
                document.body.style.cursor = '';
                if (window.currentHighlighted) {
                    removeHighlight(window.currentHighlighted);
                    window.currentHighlighted = null;
                }
            };
            
            console.log('网页元素选择脚本已注入');
        })();
        """
        
        await self.page.evaluate(highlight_script)
    
    async def _start_step_by_step_selection(self):
        """开始逐步选择流程"""
        for step_index, step in enumerate(self.config_steps):
            self.current_step = step_index
            
            print(f"\n步骤 {step_index + 1}/{len(self.config_steps)}: {step['description']}")
            print(f"提示: {step['hint']}")
            print("请在浏览器中点击相应的元素，然后按回车继续...")
            
            # 启动选择模式
            selected_element = await self._select_single_element()
            
            if selected_element:
                # 保存选择的元素
                self.selected_elements[step['name']] = selected_element
                print(f"✓ 已选择: {selected_element.get('textContent', selected_element.get('tagName', 'Unknown'))}")
                
                # 如果是密码输入框，提示用户需要手动登录
                if step['name'] == 'password_input':
                    print("\n📌 注意：系统已配置用户名和密码自动填写功能")
                    print("   启动自动化后，用户名和密码会自动填写，但需要您手动点击登录按钮")
                    print("   请确保在实际使用时手动完成登录操作")
                    input("\n理解上述说明后，请按回车继续...")
            else:
                print("❌ 未选择元素，跳过此步骤")
            
            print("-" * 50)
        
        print("\n🎉 所有元素选择完成！")
        
        # 生成最终配置
        self._generate_final_config()
    
    async def _select_single_element(self) -> Optional[Dict]:
        """选择单个元素"""
        try:
            # 启动选择模式
            await self.page.evaluate("window.startSelection()")
            
            # 等待用户选择
            selected_element = None
            max_wait_time = 300  # 5分钟超时
            wait_interval = 0.5
            elapsed_time = 0
            
            while elapsed_time < max_wait_time:
                # 检查是否有选中的元素
                element_data = await self.page.evaluate("window.selectedElement")
                if element_data:
                    selected_element = element_data
                    # 清除选中状态
                    await self.page.evaluate("window.selectedElement = null")
                    break
                
                await asyncio.sleep(wait_interval)
                elapsed_time += wait_interval
            
            # 停止选择模式
            await self.page.evaluate("window.stopSelection()")
            
            return selected_element
            
        except Exception as e:
            self.logger.error(f"元素选择失败: {str(e)}")
            return None
    
    def _generate_final_config(self):
        """生成最终配置，智能选择每个元素的最佳选择器"""
        config = {}
        used_selectors = set()  # 跟踪已使用的选择器，避免重复
        
        # 按优先级处理：优先处理重要的输入框元素
        input_elements = ['username_input', 'password_input']
        other_elements = [name for name in self.selected_elements.keys() if name not in input_elements]
        processing_order = input_elements + other_elements
        
        for element_name in processing_order:
            if element_name not in self.selected_elements:
                continue
                
            element_data = self.selected_elements[element_name]
            if not element_data or not element_data.get('selectors'):
                continue
            
            best_selector = self._select_best_unique_selector(
                element_name, element_data, used_selectors
            )
            
            if best_selector:
                config[element_name] = best_selector
                used_selectors.add(best_selector)
            else:
                # 如果没有找到唯一选择器，使用第一个可用的
                if element_data['selectors']:
                    fallback_selector = element_data['selectors'][0]['selector']
                    config[element_name] = fallback_selector
                    self.logger.warning(f"{element_name} 使用可能重复的选择器: {fallback_selector}")
        
        self.selected_elements = config
        print(f"\n✅ 生成的优化配置:")
        for key, value in config.items():
            print(f"  {key}: {value}")
    
    def _select_best_unique_selector(self, element_name: str, element_data: dict, used_selectors: set) -> str:
        """为元素选择最佳的唯一选择器"""
        selectors = element_data.get('selectors', [])
        tag_name = element_data.get('tagName', '').lower()
        element_type = element_data.get('type', '')
        element_name_attr = element_data.get('name', '')
        
        # 对于输入框元素，应用特殊逻辑
        if tag_name == 'input' and element_name in ['username_input', 'password_input']:
            return self._select_input_selector(element_name, element_data, used_selectors)
        
        # 对于其他元素，按优先级选择第一个未使用的选择器
        for selector_info in selectors:
            selector = selector_info['selector']
            if selector not in used_selectors:
                return selector
        
        # 如果所有选择器都被使用，返回优先级最高的
        return selectors[0]['selector'] if selectors else None
    
    def _select_input_selector(self, element_name: str, element_data: dict, used_selectors: set) -> str:
        """为输入框元素选择最佳选择器"""
        selectors = element_data.get('selectors', [])
        element_type = element_data.get('type', '')
        element_name_attr = element_data.get('name', '')
        placeholder = element_data.get('placeholder', '')
        
        # 预期的选择器类型优先级（针对输入框）
        preferred_types = [
            'type_name_combo',      # input[type="text"][name="username"]
            'name',                 # input[name="username"] 
            'type',                 # input[type="password"]
            'type_placeholder_combo', # input[type="text"][placeholder*="用户名"]
            'placeholder',          # input[placeholder*="用户名"]
            'parent_type_context',  # .login-form input[type="text"]
            'class_simple',         # .username-input
            'parent_context'        # .login-form input
        ]
        
        # 根据元素名称和类型进行智能匹配
        if element_name == 'username_input':
            # 用户名输入框优先使用text类型相关的选择器
            type_keywords = ['text', 'email', 'tel']
            name_keywords = ['user', 'login', 'account', 'email']
        elif element_name == 'password_input': 
            # 密码输入框优先使用password类型选择器
            type_keywords = ['password']
            name_keywords = ['pass', 'pwd']
        else:
            type_keywords = []
            name_keywords = []
        
        # 按优先级类型查找最佳选择器
        for selector_type in preferred_types:
            for selector_info in selectors:
                if (selector_info['type'] == selector_type and 
                    selector_info['selector'] not in used_selectors):
                    
                    # 额外验证：确保选择器与元素类型匹配
                    selector = selector_info['selector']
                    if self._validate_input_selector(selector, element_name, type_keywords, name_keywords):
                        return selector
        
        # 如果没有找到理想的选择器，选择第一个未使用的
        for selector_info in selectors:
            selector = selector_info['selector']
            if selector not in used_selectors:
                return selector
        
        # 最后的备选：返回第一个选择器
        return selectors[0]['selector'] if selectors else None
    
    def _validate_input_selector(self, selector: str, element_name: str, type_keywords: list, name_keywords: list) -> bool:
        """验证输入框选择器是否与元素类型匹配"""
        selector_lower = selector.lower()
        
        # 检查type属性匹配
        for keyword in type_keywords:
            if f'type="{keyword}"' in selector_lower:
                return True
        
        # 检查name属性匹配  
        for keyword in name_keywords:
            if f'name="' in selector_lower and keyword in selector_lower:
                return True
        
        # 对于组合选择器，更宽松的匹配
        if any(keyword in selector_lower for keyword in type_keywords + name_keywords):
            return True
        
        # 默认接受（避免过度严格）
        return True
    
    async def _cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            self.logger.error(f"清理资源时出错: {str(e)}")


class WebElementSelectorManager:
    """网页元素选择器管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def start_interactive_selection(self, target_url: str = None) -> Optional[Dict]:
        """
        启动交互式元素选择
        
        Args:
            target_url: 目标网站URL
            
        Returns:
            选择器配置字典
        """
        try:
            selector = WebElementSelector()
            
            # 运行异步选择流程
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(selector.start_selection(target_url))
            loop.close()
            
            return result
            
        except Exception as e:
            self.logger.error(f"交互式元素选择失败: {str(e)}")
            return None
    
    def save_selectors_to_config(self, selectors: Dict, config_file: str = "web_element_selectors.json"):
        """
        保存选择器到配置文件
        
        Args:
            selectors: 选择器配置字典
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(selectors, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"选择器配置已保存到 {config_file}")
            
        except Exception as e:
            self.logger.error(f"保存选择器配置失败: {str(e)}")
    
    def load_selectors_from_config(self, config_file: str = "web_element_selectors.json") -> Optional[Dict]:
        """
        从配置文件加载选择器
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            选择器配置字典
        """
        try:
            import os
            if not os.path.exists(config_file):
                return None
            
            with open(config_file, 'r', encoding='utf-8') as f:
                selectors = json.load(f)
            
            self.logger.info(f"从 {config_file} 加载选择器配置")
            return selectors
            
        except Exception as e:
            self.logger.error(f"加载选择器配置失败: {str(e)}")
            return None


# 全局管理器实例
_selector_manager = None

def get_web_element_selector_manager() -> WebElementSelectorManager:
    """获取全局网页元素选择器管理器实例"""
    global _selector_manager
    if _selector_manager is None:
        _selector_manager = WebElementSelectorManager()
    return _selector_manager


if __name__ == "__main__":
    # 测试代码
    import sys
    logging.basicConfig(level=logging.INFO)
    
    manager = WebElementSelectorManager()
    
    print("开始网页元素选择测试...")
    selectors = manager.start_interactive_selection()
    
    if selectors:
        print(f"\n选择完成，结果: {selectors}")
        manager.save_selectors_to_config(selectors, "test_selectors.json")
    else:
        print("选择失败或取消")