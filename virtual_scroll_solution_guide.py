# -*- coding: utf-8 -*-
"""
虚拟滚动问题解决方案 - 使用指南
"""

import os
import sys
import asyncio
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('virtual_scroll_debug.log', encoding='utf-8')
        ]
    )

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🔧 虚拟滚动问题解决方案")
    print("   专门解决c-virtual-group获取全部内容的问题")
    print("=" * 60)

async def diagnose_virtual_scroll_issue():
    """诊断虚拟滚动问题"""
    print("\n📋 步骤1: 诊断当前问题")
    print("-" * 30)
    
    # 导入调试工具
    try:
        from debug_virtual_scroll import VirtualScrollDebugger
        
        debugger = VirtualScrollDebugger()
        
        print("请提供需要调试的网页URL")
        print("（这应该是包含c-virtual-group的股票数据页面）")
        url = input("URL: ").strip()
        
        if not url:
            print("❌ 未提供URL，跳过诊断步骤")
            return False
        
        print(f"\n🔍 正在诊断网页: {url}")
        await debugger.debug_virtual_scroll_elements(url)
        
        return True
        
    except ImportError:
        print("❌ 调试工具导入失败，请确保debug_virtual_scroll.py文件存在")
        return False
    except Exception as e:
        print(f"❌ 诊断过程出错: {e}")
        return False

def integrate_optimized_solution():
    """集成优化解决方案"""
    print("\n🔧 步骤2: 集成优化解决方案")
    print("-" * 30)
    
    try:
        # 检查必要文件是否存在
        required_files = [
            'web_automator.py',
            'optimized_virtual_scroll.py'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
            return False
        
        print("✅ 所有必要文件都存在")
        
        # 提供集成选项
        print("\n选择集成方式:")
        print("1. 替换现有的滚动方法（推荐）")
        print("2. 添加为新的滚动方法")
        print("3. 创建独立的测试脚本")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            backup_and_replace_method()
        elif choice == "2":
            add_new_method()
        elif choice == "3":
            create_test_script()
        else:
            print("❌ 无效选择")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 集成过程出错: {e}")
        return False

def backup_and_replace_method():
    """备份并替换现有方法"""
    print("\n🔄 替换现有滚动方法...")
    
    try:
        # 备份原文件
        import shutil
        backup_file = 'web_automator_backup.py'
        shutil.copy2('web_automator.py', backup_file)
        print(f"✅ 已备份原文件到: {backup_file}")
        
        # 读取原文件
        with open('web_automator.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在文件开头添加导入
        import_line = "from optimized_virtual_scroll import OptimizedVirtualScrollHandler\n"
        if import_line not in content:
            # 找到合适的位置插入import
            lines = content.split('\n')
            import_inserted = False
            
            for i, line in enumerate(lines):
                if line.startswith('from ') and 'config' in line.lower():
                    lines.insert(i + 1, import_line.strip())
                    import_inserted = True
                    break
            
            if not import_inserted:
                # 如果没找到合适位置，在第一个import后插入
                for i, line in enumerate(lines):
                    if line.startswith('import ') or line.startswith('from '):
                        lines.insert(i + 1, import_line.strip())
                        break
            
            content = '\n'.join(lines)
        
        # 替换_scroll_and_load_all_data方法
        replacement_method = '''
    async def _scroll_and_load_all_data(self, progress_callback=None):
        """
        优化版滚动加载所有数据（使用OptimizedVirtualScrollHandler）
        
        Args:
            progress_callback: 进度回调函数，接收 (scroll_count, current_rows, message) 参数
            
        Returns:
            tuple: (success, message, final_row_count)
        """
        try:
            # 使用优化的虚拟滚动处理器
            handler = OptimizedVirtualScrollHandler(self.page, self.logger)
            return await handler.scroll_and_load_all_data(progress_callback)
            
        except Exception as e:
            error_msg = f"优化滚动加载过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, 0
'''
        
        # 使用正则表达式找到并替换原方法
        import re
        
        # 找到原方法的开始和结束位置
        pattern = r'async def _scroll_and_load_all_data\(self.*?\n(.*?)(?=\n    async def|\n    def|\nclass|\n\n\nclass|\Z)'
        
        if re.search(pattern, content, re.DOTALL):
            content = re.sub(pattern, replacement_method.strip(), content, flags=re.DOTALL)
            print("✅ 已替换_scroll_and_load_all_data方法")
        else:
            print("⚠️ 未找到原方法，将在类的末尾添加新方法")
            # 找到WebAutomator类的末尾并添加方法
            class_pattern = r'(class WebAutomator:.*?)(\nclass|\n\n\n|\Z)'
            match = re.search(class_pattern, content, re.DOTALL)
            if match:
                class_content = match.group(1)
                remaining = match.group(2) if match.group(2) else ""
                new_class_content = class_content + "\n" + replacement_method
                content = content.replace(match.group(0), new_class_content + remaining)
        
        # 写回文件
        with open('web_automator.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已成功替换滚动方法")
        print(f"📁 备份文件: {backup_file}")
        print("🔄 请重启应用程序以使用新的滚动逻辑")
        
    except Exception as e:
        print(f"❌ 替换方法时出错: {e}")

def add_new_method():
    """添加新的滚动方法"""
    print("\n➕ 添加新的滚动方法...")
    
    try:
        with open('web_automator.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加导入
        import_line = "from optimized_virtual_scroll import OptimizedVirtualScrollHandler"
        if import_line not in content:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('from ') and 'config' in line.lower():
                    lines.insert(i + 1, import_line)
                    break
            content = '\n'.join(lines)
        
        # 添加新方法
        new_method = '''
    async def _scroll_and_load_all_data_optimized(self, progress_callback=None):
        """
        优化版滚动加载所有数据
        可以通过修改extract_stock_data方法调用此方法来测试效果
        """
        try:
            handler = OptimizedVirtualScrollHandler(self.page, self.logger)
            return await handler.scroll_and_load_all_data(progress_callback)
        except Exception as e:
            error_msg = f"优化滚动加载过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, 0
'''
        
        # 在WebAutomator类的末尾添加方法
        import re
        class_pattern = r'(class WebAutomator:.*?)(\nclass|\n\n\n|\Z)'
        match = re.search(class_pattern, content, re.DOTALL)
        if match:
            class_content = match.group(1)
            remaining = match.group(2) if match.group(2) else ""
            new_class_content = class_content + "\n" + new_method
            content = content.replace(match.group(0), new_class_content + remaining)
        
        with open('web_automator.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已添加新的滚动方法: _scroll_and_load_all_data_optimized")
        print("📝 要使用新方法，请在extract_stock_data方法中将:")
        print("   scroll_success, scroll_message, final_row_count = await self._scroll_and_load_all_data(progress_callback)")
        print("   替换为:")
        print("   scroll_success, scroll_message, final_row_count = await self._scroll_and_load_all_data_optimized(progress_callback)")
        
    except Exception as e:
        print(f"❌ 添加方法时出错: {e}")

def create_test_script():
    """创建独立测试脚本"""
    print("\n📄 创建独立测试脚本...")
    
    test_script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟滚动优化测试脚本
独立测试优化的滚动功能
"""

import asyncio
import logging
from playwright.async_api import async_playwright
from optimized_virtual_scroll import OptimizedVirtualScrollHandler

async def test_optimized_virtual_scroll():
    """测试优化的虚拟滚动功能"""
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    playwright = None
    browser = None
    
    try:
        # 获取测试URL
        url = input("请输入要测试的网页URL: ").strip()
        if not url:
            print("❌ 未提供URL")
            return
        
        # 启动浏览器
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context(viewport={'width': 1920, 'height': 1080})
        page = await context.new_page()
        
        # 导航到页面
        print(f"🔍 正在访问: {url}")
        await page.goto(url)
        await page.wait_for_load_state('networkidle')
        
        print("📋 请手动完成登录并导航到数据页面，然后按回车继续...")
        input()
        
        # 创建优化的滚动处理器
        handler = OptimizedVirtualScrollHandler(page, logger)
        
        # 定义进度回调
        def progress_callback(scroll_count, data_rows, message):
            print(f"📊 {message}")
        
        # 执行滚动测试
        print("🚀 开始执行优化滚动测试...")
        success, message, final_count = await handler.scroll_and_load_all_data(progress_callback)
        
        if success:
            print(f"✅ 测试成功: {message}")
            print(f"📈 最终获取数据行数: {final_count}")
        else:
            print(f"❌ 测试失败: {message}")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    finally:
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()

if __name__ == "__main__":
    asyncio.run(test_optimized_virtual_scroll())
'''
    
    try:
        with open('test_optimized_scroll.py', 'w', encoding='utf-8') as f:
            f.write(test_script_content)
        
        print("✅ 已创建独立测试脚本: test_optimized_scroll.py")
        print("🚀 运行测试:")
        print("   python test_optimized_scroll.py")
        
    except Exception as e:
        print(f"❌ 创建测试脚本时出错: {e}")

def provide_usage_tips():
    """提供使用建议"""
    print("\n💡 使用建议和故障排除")
    print("-" * 30)
    
    tips = [
        "1. 🔍 首先运行调试工具确认虚拟滚动结构:",
        "   python debug_virtual_scroll.py",
        "",
        "2. 📋 如果检测到c-virtual-group结构，使用优化方案",
        "   如果没有检测到，可能需要:",
        "   - 检查网页是否正确加载",
        "   - 确认是否在正确的数据页面",
        "   - 查看网页是否使用了其他虚拟滚动库",
        "",
        "3. 🔧 集成优化方案后，注意:",
        "   - 备份原文件已创建",
        "   - 重启应用程序生效",
        "   - 监控日志输出了解滚动过程",
        "",
        "4. ⚠️ 常见问题:",
        "   - 如果仍然只获取第一屏: 检查transform值是否正确获取",
        "   - 如果滚动过快结束: 调整stable_threshold参数",
        "   - 如果网络超时: 增加等待时间",
        "",
        "5. 🎯 性能优化:",
        "   - 优化版本减少了不必要的检测",
        "   - 简化了完成判断逻辑",
        "   - 增加了滚动距离以减少滚动次数",
        "",
        "6. 📊 监控和调试:",
        "   - 查看virtual_scroll_debug.log日志文件",
        "   - 使用progress_callback监控进度",
        "   - 在浏览器开发者工具中观察DOM变化"
    ]
    
    for tip in tips:
        print(tip)

async def main():
    """主函数"""
    setup_logging()
    print_banner()
    
    try:
        # 步骤1: 诊断问题
        diagnose_success = await diagnose_virtual_scroll_issue()
        
        # 步骤2: 集成解决方案
        if diagnose_success or input("\n是否跳过诊断直接集成解决方案? (y/n): ").lower() == 'y':
            integrate_optimized_solution()
        
        # 步骤3: 提供使用建议
        provide_usage_tips()
        
        print("\n🎉 解决方案部署完成!")
        print("📖 建议按照上述使用建议进行测试和调优")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())