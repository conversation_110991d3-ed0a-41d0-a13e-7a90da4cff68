# -*- coding: utf-8 -*-
"""
GUI统一信号操作模块
包含买入和卖出信号监控的所有GUI操作和线程管理的统一实现
"""

import threading
import tkinter as tk
from tkinter import messagebox, filedialog
import logging
from typing import Dict, Any

from signal_analyzer import SignalAnalyzer
from buy_signal_monitor import BuySignalMonitor
from sell_signal_monitor import SellSignalMonitor


class GUISignalOperationsMixin:
    """GUI统一信号操作Mixin类"""
    
    def init_signal_components(self):
        """初始化信号相关组件"""
        try:
            # 初始化买入信号监控状态
            self.is_buy_signal_monitoring = False
            self.buy_signal_monitor = None
            self.buy_signal_analyzer = None
            
            # 初始化卖出信号监控状态
            self.is_sell_signal_monitoring = False
            self.sell_signal_monitor = None
            self.sell_signal_analyzer = None
            
            # 加载信号区域配置（买入和卖出共用同一区域）
            self.load_saved_buy_signal_region()
            
            self.logger.info("统一信号组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"统一信号组件初始化失败: {str(e)}")
    
    def start_signal_monitoring(self, signal_type: str = 'buy'):
        """
        启动信号监控
        
        Args:
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        try:
            # 检查前置条件
            if not self._check_signal_prerequisites():
                return
            
            # 检查是否已在运行
            is_monitoring = (self.is_buy_signal_monitoring if signal_type == 'buy' 
                           else self.is_sell_signal_monitoring)
            if is_monitoring:
                signal_name = "买入" if signal_type == 'buy' else "卖出"
                messagebox.showwarning("警告", f"{signal_name}信号监控已在运行中")
                return
            
            # 获取Excel文件路径
            excel_path = self.excel_path_var.get().strip()
            if not excel_path:
                messagebox.showerror("错误", "请先选择Excel文件")
                return
            
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            self.logger.info(f"开始启动{signal_name}信号监控")
            
            # 检查并建立指南针连接
            if not self._ensure_compass_connection():
                return
            
            # 创建分析器和监控器
            if not self._create_signal_components(signal_type):
                return
            
            # 在新线程中启动监控
            self._start_monitoring_thread(excel_path, signal_type)
            
        except Exception as e:
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            self.logger.error(f"启动{signal_name}信号监控失败: {str(e)}")
            messagebox.showerror("错误", f"启动监控失败: {str(e)}")
    
    def start_buy_signal_monitoring(self):
        """启动买入信号监控"""
        self.start_signal_monitoring('buy')
    
    def start_sell_signal_monitoring(self):
        """启动卖出信号监控"""
        self.start_signal_monitoring('sell')
    
    def stop_signal_monitoring(self, signal_type: str = 'buy'):
        """
        停止信号监控
        
        Args:
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        try:
            is_monitoring = (self.is_buy_signal_monitoring if signal_type == 'buy' 
                           else self.is_sell_signal_monitoring)
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            
            if not is_monitoring:
                messagebox.showinfo("提示", f"{signal_name}信号监控未在运行")
                return
            
            self.logger.info(f"正在停止{signal_name}信号监控")
            
            # 停止监控
            monitor = (self.buy_signal_monitor if signal_type == 'buy' 
                      else self.sell_signal_monitor)
            if monitor:
                monitor.stop_monitoring()
            
            # 更新UI状态
            self._update_signal_ui_state(signal_type, False)
            
            self.message_queue.put(("log", f"🛑 用户请求停止{signal_name}信号监控"))
            
        except Exception as e:
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            self.logger.error(f"停止{signal_name}信号监控失败: {str(e)}")
            messagebox.showerror("错误", f"停止监控失败: {str(e)}")
    
    def stop_buy_signal_monitoring(self):
        """停止买入信号监控"""
        self.stop_signal_monitoring('buy')
    
    def stop_sell_signal_monitoring(self):
        """停止卖出信号监控"""
        self.stop_signal_monitoring('sell')
    
    def test_signal_recognition(self, signal_type: str = 'buy'):
        """
        测试信号识别
        
        Args:
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        try:
            # 检查信号区域配置
            if not hasattr(self, 'selected_buy_signal_region') or not self.selected_buy_signal_region:
                messagebox.showwarning("警告", "请先选择信号区域")
                return
            
            if not self.ocr_manager.is_initialized():
                messagebox.showerror("错误", "OCR引擎未初始化")
                return
            
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            self.logger.info(f"开始测试{signal_name}信号识别")
            
            # 创建分析器
            from config import COMPASS_SOFTWARE
            signal_region = COMPASS_SOFTWARE.get('buy_signal_region', {})
            analyzer = SignalAnalyzer(self.ocr_manager, signal_region)
            
            # 在新线程中执行测试
            def test_thread():
                try:
                    self.message_queue.put(("log", f"🔍 开始{signal_name}信号识别测试"))
                    
                    # 执行测试
                    result = analyzer.test_signal_recognition()
                    
                    # 发送结果消息
                    self.message_queue.put((f"{signal_type}_signal_test_result", result))
                    
                except Exception as e:
                    self.message_queue.put((f"{signal_type}_signal_test_error", str(e)))
            
            threading.Thread(target=test_thread, daemon=True).start()
            
        except Exception as e:
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            self.logger.error(f"{signal_name}信号识别测试失败: {str(e)}")
            messagebox.showerror("错误", f"测试失败: {str(e)}")
    
    def test_buy_signal_recognition(self):
        """测试买入信号识别"""
        self.test_signal_recognition('buy')
    
    def test_sell_signal_recognition(self):
        """测试卖出信号识别"""
        self.test_signal_recognition('sell')
    
    def export_signal_history(self, signal_type: str = 'buy'):
        """
        导出信号变化历史
        
        Args:
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        try:
            monitor = (self.buy_signal_monitor if signal_type == 'buy' 
                      else self.sell_signal_monitor)
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            
            if not monitor or not monitor.get_signal_states():
                messagebox.showinfo("提示", f"没有{signal_name}信号历史数据可导出")
                return
            
            # 选择保存路径
            filepath = filedialog.asksaveasfilename(
                title=f"保存{signal_name}信号历史",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
            )
            
            if not filepath:
                return
            
            # 导出历史
            success = monitor.export_signal_history(filepath)
            
            if success:
                self.message_queue.put(("log", f"📁 {signal_name}信号历史已导出到: {filepath}"))
                messagebox.showinfo("成功", f"信号历史已导出到:\n{filepath}")
            else:
                messagebox.showerror("失败", f"导出{signal_name}信号历史失败")
                
        except Exception as e:
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            self.logger.error(f"导出{signal_name}信号历史失败: {str(e)}")
            messagebox.showerror("错误", f"导出失败: {str(e)}")
    
    def export_buy_signal_history(self):
        """导出买入信号变化历史"""
        self.export_signal_history('buy')
    
    def export_sell_signal_history(self):
        """导出卖出信号变化历史"""
        self.export_signal_history('sell')
    
    def _check_signal_prerequisites(self) -> bool:
        """检查信号监控的前置条件"""
        # 检查信号区域配置
        if not hasattr(self, 'selected_buy_signal_region') or not self.selected_buy_signal_region:
            messagebox.showerror("错误", "请先选择信号区域")
            return False
        
        # 检查OCR初始化
        if not self.ocr_manager.is_initialized():
            messagebox.showerror("错误", "OCR引擎未初始化，请重启程序")
            return False
        
        return True
    
    def _ensure_compass_connection(self) -> bool:
        """确保指南针软件连接"""
        try:
            self.logger.info("检查指南针软件连接状态")
            
            # 检查是否已有有效连接
            if self.compass_automator and self.compass_automator.check_connection_valid():
                self.logger.info("使用已存在的指南针连接")
                return True
            
            # 如果没有实例或连接失效，尝试连接
            if not self.compass_automator:
                self.logger.info("创建指南针自动化器实例")
                from compass_automator import CompassAutomator
                self.compass_automator = CompassAutomator(ocr_manager=self.ocr_manager)
            else:
                self.logger.info("指南针连接已断开，尝试重新连接")
            
            # 尝试连接到指南针软件
            self.logger.info("正在连接指南针软件...")
            success = self.compass_automator.start_compass_software()
            
            if success:
                self.logger.info("指南针软件连接成功")
                return True
            else:
                self.logger.error("指南针软件连接失败")
                messagebox.showerror("连接失败", 
                    "无法连接到指南针软件，请确保：\n"
                    "1. 指南针软件已启动\n"
                    "2. 软件界面可见\n"
                    "3. 没有其他程序占用指南针窗口")
                return False
                
        except Exception as e:
            self.logger.error(f"连接指南针软件时出错: {str(e)}")
            messagebox.showerror("连接错误", f"连接指南针软件时出错: {str(e)}")
            return False
    
    def _create_signal_components(self, signal_type: str) -> bool:
        """
        创建信号分析器和监控器
        
        Args:
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        try:
            from config import COMPASS_SOFTWARE, SIGNAL_CONFIG
            
            # 创建信号分析器
            signal_region = COMPASS_SOFTWARE.get('buy_signal_region', {})
            analyzer = SignalAnalyzer(self.ocr_manager, signal_region)
            
            # 创建信号监控器
            if signal_type == 'buy':
                self.buy_signal_analyzer = analyzer
                self.buy_signal_monitor = BuySignalMonitor(
                    self.compass_automator,
                    self.buy_signal_analyzer,
                    SIGNAL_CONFIG
                )
            else:  # sell
                self.sell_signal_analyzer = analyzer
                self.sell_signal_monitor = SellSignalMonitor(
                    self.compass_automator,
                    self.sell_signal_analyzer,
                    SIGNAL_CONFIG
                )
            
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            self.logger.info(f"{signal_name}信号组件创建成功")
            return True
            
        except Exception as e:
            signal_name = "买入" if signal_type == 'buy' else "卖出"
            self.logger.error(f"创建{signal_name}信号组件失败: {str(e)}")
            messagebox.showerror("错误", f"创建监控组件失败: {str(e)}")
            return False
    
    def _start_monitoring_thread(self, excel_path: str, signal_type: str):
        """
        启动监控线程
        
        Args:
            excel_path: Excel文件路径
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        def monitoring_thread():
            try:
                signal_name = "买入" if signal_type == 'buy' else "卖出"
                monitor = (self.buy_signal_monitor if signal_type == 'buy' 
                          else self.sell_signal_monitor)
                
                # 读取股票代码
                self.message_queue.put(("log", f"📋 正在读取Excel文件: {excel_path}"))
                stock_codes = monitor.load_stock_codes(excel_path)
                
                if not stock_codes:
                    self.message_queue.put(("log", "❌ 未找到有效的股票代码"))
                    self.message_queue.put((f"{signal_type}_signal_monitoring_stopped", None))
                    return
                
                # 在开始监控前再次验证指南针连接
                self.message_queue.put(("log", "🔗 验证指南针软件连接状态"))
                if not self.compass_automator or not self.compass_automator.check_connection_valid():
                    self.message_queue.put(("log", "❌ 指南针连接已断开，无法开始监控"))
                    self.message_queue.put((f"{signal_type}_signal_monitoring_stopped", None))
                    return
                
                self.message_queue.put(("log", "✅ 指南针连接验证成功"))
                
                # 更新UI状态
                self.message_queue.put((f"{signal_type}_signal_monitoring_started", len(stock_codes)))
                
                # 启动监控
                success = monitor.start_monitoring(stock_codes, self.message_queue)
                
                if not success:
                    self.message_queue.put(("log", "❌ 监控启动失败"))
                    self.message_queue.put((f"{signal_type}_signal_monitoring_stopped", None))
                
            except Exception as e:
                self.logger.error(f"监控线程出错: {str(e)}")
                self.message_queue.put(("log", f"❌ 监控线程出错: {str(e)}"))
                self.message_queue.put((f"{signal_type}_signal_monitoring_stopped", None))
        
        # 启动线程
        threading.Thread(target=monitoring_thread, daemon=True).start()
    
    def _update_signal_ui_state(self, signal_type: str, is_monitoring: bool):
        """
        更新信号监控的UI状态
        
        Args:
            signal_type: 信号类型 ('buy' 或 'sell')
            is_monitoring: 是否正在监控
        """
        if signal_type == 'buy':
            self.is_buy_signal_monitoring = is_monitoring
            
            if hasattr(self, 'buy_signal_start_btn'):
                self.buy_signal_start_btn.config(state=tk.DISABLED if is_monitoring else tk.NORMAL)
            
            if hasattr(self, 'buy_signal_stop_btn'):
                self.buy_signal_stop_btn.config(state=tk.NORMAL if is_monitoring else tk.DISABLED)
            
            # 禁用卖出信号监控按钮，防止同时运行
            if hasattr(self, 'sell_signal_start_btn'):
                self.sell_signal_start_btn.config(state=tk.DISABLED if is_monitoring else tk.NORMAL)
        
        else:  # sell
            self.is_sell_signal_monitoring = is_monitoring
            
            if hasattr(self, 'sell_signal_start_btn'):
                self.sell_signal_start_btn.config(state=tk.DISABLED if is_monitoring else tk.NORMAL)
            
            if hasattr(self, 'sell_signal_stop_btn'):
                self.sell_signal_stop_btn.config(state=tk.NORMAL if is_monitoring else tk.DISABLED)
            
            # 禁用买入信号监控按钮，防止同时运行
            if hasattr(self, 'buy_signal_start_btn'):
                self.buy_signal_start_btn.config(state=tk.DISABLED if is_monitoring else tk.NORMAL)
        
        # 禁用/启用其他操作按钮
        if hasattr(self, 'start_btn'):
            any_monitoring = self.is_buy_signal_monitoring or self.is_sell_signal_monitoring
            self.start_btn.config(state=tk.DISABLED if any_monitoring else tk.NORMAL)
    
    def handle_signal_messages(self, message_type: str, data):
        """处理信号相关的消息"""
        try:
            # 处理买入信号消息
            if message_type.startswith('buy_signal'):
                if message_type == "buy_signal_monitoring_started":
                    stock_count = data
                    self._update_signal_ui_state('buy', True)
                    self.logger.info(f"买入信号监控已启动，股票数量: {stock_count}")
                    
                elif message_type == "buy_signal_monitoring_stopped":
                    self._update_signal_ui_state('buy', False)
                    self.logger.info("买入信号监控已停止")
                    
                elif message_type == "buy_signal_test_result":
                    result = data
                    self._handle_signal_test_result(result, '买入')
                    
                elif message_type == "buy_signal_test_error":
                    error_msg = data
                    self.logger.error(f"买入信号测试失败: {error_msg}")
                    messagebox.showerror("测试失败", f"买入信号识别测试失败:\n{error_msg}")
            
            # 处理卖出信号消息
            elif message_type.startswith('sell_signal'):
                if message_type == "sell_signal_monitoring_started":
                    stock_count = data
                    self._update_signal_ui_state('sell', True)
                    self.logger.info(f"卖出信号监控已启动，股票数量: {stock_count}")
                    
                elif message_type == "sell_signal_monitoring_stopped":
                    self._update_signal_ui_state('sell', False)
                    self.logger.info("卖出信号监控已停止")
                    
                elif message_type == "sell_signal_test_result":
                    result = data
                    self._handle_signal_test_result(result, '卖出')
                    
                elif message_type == "sell_signal_test_error":
                    error_msg = data
                    self.logger.error(f"卖出信号测试失败: {error_msg}")
                    messagebox.showerror("测试失败", f"卖出信号识别测试失败:\n{error_msg}")
                    
        except Exception as e:
            self.logger.error(f"处理信号消息失败: {str(e)}")
    
    def handle_buy_signal_messages(self, message_type: str, data):
        """处理买入信号相关的消息（向后兼容）"""
        self.handle_signal_messages(message_type, data)
    
    def handle_sell_signal_messages(self, message_type: str, data):
        """处理卖出信号相关的消息（向后兼容）"""
        self.handle_signal_messages(message_type, data)
    
    def _handle_signal_test_result(self, result: dict, signal_name: str):
        """处理信号识别测试结果"""
        try:
            if result['success']:
                signal = result.get('signal')
                raw_text = result.get('raw_text', '')
                cleaned_text = result.get('cleaned_text', '')
                is_valid = result.get('is_valid', False)
                
                # 记录测试结果日志
                if is_valid and signal:
                    self.message_queue.put(("log", f"✅ {signal_name}信号识别测试成功: {signal}"))
                    test_msg = f"{signal_name}信号识别测试成功！\n\n识别结果: {signal}\n原始OCR: {raw_text}\n清洗后: {cleaned_text}"
                    messagebox.showinfo("测试成功", test_msg)
                else:
                    self.message_queue.put(("log", f"⚠️ {signal_name}信号识别测试无效结果: {cleaned_text}"))
                    test_msg = f"{signal_name}信号识别测试完成，但结果无效\n\n原始OCR: {raw_text}\n清洗后: {cleaned_text}\n\n请检查信号区域选择是否正确"
                    messagebox.showwarning("测试结果", test_msg)
            else:
                error = result.get('error', '未知错误')
                self.message_queue.put(("log", f"❌ {signal_name}信号识别测试失败: {error}"))
                messagebox.showerror("测试失败", f"{signal_name}信号识别测试失败:\n{error}")
                
        except Exception as e:
            self.logger.error(f"处理信号测试结果失败: {str(e)}")
    
    def get_signal_monitoring_stats(self, signal_type: str = 'buy') -> dict:
        """
        获取信号监控统计信息
        
        Args:
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        monitor = (self.buy_signal_monitor if signal_type == 'buy' 
                  else self.sell_signal_monitor)
        if monitor:
            return monitor.get_monitoring_stats()
        return {}
    
    def get_buy_signal_monitoring_stats(self) -> dict:
        """获取买入信号监控统计信息（向后兼容）"""
        return self.get_signal_monitoring_stats('buy')
    
    def get_sell_signal_monitoring_stats(self) -> dict:
        """获取卖出信号监控统计信息（向后兼容）"""
        return self.get_signal_monitoring_stats('sell')
    
    def get_signal_states(self, signal_type: str = 'buy') -> dict:
        """
        获取信号状态
        
        Args:
            signal_type: 信号类型 ('buy' 或 'sell')
        """
        monitor = (self.buy_signal_monitor if signal_type == 'buy' 
                  else self.sell_signal_monitor)
        if monitor:
            return monitor.get_signal_states()
        return {}
    
    def get_buy_signal_states(self) -> dict:
        """获取买入信号状态（向后兼容）"""
        return self.get_signal_states('buy')
    
    def get_sell_signal_states(self) -> dict:
        """获取卖出信号状态（向后兼容）"""
        return self.get_signal_states('sell')
    
    def get_stocks_by_signal(self, signal: str, signal_type: str = 'buy') -> list:
        """
        获取具有指定信号的股票列表
        
        Args:
            signal: 信号类型
            signal_type: 监控类型 ('buy' 或 'sell')
        """
        monitor = (self.buy_signal_monitor if signal_type == 'buy' 
                  else self.sell_signal_monitor)
        if monitor:
            return monitor.get_stocks_by_signal(signal)
        return []
    
    def get_stocks_by_buy_signal(self, signal: str) -> list:
        """获取具有指定买入信号的股票列表（向后兼容）"""
        return self.get_stocks_by_signal(signal, 'buy')
    
    def get_stocks_by_sell_signal(self, signal: str) -> list:
        """获取具有指定卖出信号的股票列表（向后兼容）"""
        return self.get_stocks_by_signal(signal, 'sell')


# 为了向后兼容，保留原有的类名
class GUIBuySignalOperationsMixin(GUISignalOperationsMixin):
    """GUI买入信号操作Mixin类（向后兼容包装）"""
    
    def init_buy_signal_components(self):
        """初始化买入信号相关组件（向后兼容）"""
        self.init_signal_components()


class GUISellSignalOperationsMixin(GUISignalOperationsMixin):
    """GUI卖出信号操作Mixin类（向后兼容包装）"""
    
    def init_sell_signal_components(self):
        """初始化卖出信号相关组件（向后兼容）"""
        self.init_signal_components()