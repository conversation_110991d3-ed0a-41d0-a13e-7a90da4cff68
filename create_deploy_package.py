# -*- coding: utf-8 -*-
"""
股票筛选器部署包生成器
用于创建便携式源码部署包，支持一键部署和增量更新
"""

import os
import shutil
import zipfile
import datetime
import json
import re
from pathlib import Path

class DeployPackageCreator:
    def __init__(self, source_dir=None, output_dir="deploy_packages"):
        """
        初始化部署包生成器
        
        Args:
            source_dir: 源码目录，默认为当前目录
            output_dir: 输出目录，默认为 deploy_packages
        """
        self.source_dir = Path(source_dir) if source_dir else Path(".")
        self.output_dir = Path(output_dir)
        self.version_info = self.generate_version_info()
        
        # 需要包含的核心文件模式
        self.include_patterns = [
            "*.py",          # Python源码文件
            "requirements.txt", # 依赖文件
            "CLAUDE.md",     # 项目说明
            "*.md",          # 其他说明文档
        ]
        
        # 需要排除的文件和目录模式（基于.gitignore）
        self.exclude_patterns = [
            "__pycache__",
            "*.pyc",
            "*.pyo",
            "*.pyd",
            ".Python",
            "build",
            "dist",
            "*.egg-info",
            "test_*.py",      # 测试文件
            "*_test.py",      # 测试文件
            "tests",          # 测试目录
            "debug_images*",  # 调试图片目录
            "ocr_debug_images", # OCR调试目录
            "*.png",          # 图片文件
            "*.jpg",          # 图片文件
            "*.jpeg",         # 图片文件
            "*.log",          # 日志文件
            "logs",           # 日志目录
            "data/",          # 数据目录（仅目录）
            "*.xlsx",         # Excel文件
            "*.xls",          # Excel文件
            "temp",           # 临时目录
            "tmp",            # 临时目录
            ".vscode",        # IDE配置
            ".idea",          # IDE配置
            "*.bat",          # 现有的批处理文件（会生成新的）
            "*.sh",           # Shell脚本
            "install.*",      # 安装脚本
            "start.*",        # 启动脚本
            "config_backup.py", # 配置备份
            "deploy_packages", # 输出目录本身
            ".git",           # Git目录
            ".gitignore",     # Git忽略文件
        ]
        
        # 仅排除目录的模式
        self.exclude_dir_patterns = [
            "data",           # 数据目录
            "pic",            # 图片目录
            "scripts",        # 脚本目录
            "build",          # 构建目录
            "dist",           # 分发目录
        ]
    
    def generate_version_info(self):
        """生成版本信息"""
        now = datetime.datetime.now()
        version = f"v{now.strftime('%Y.%m.%d')}.{now.strftime('%H%M')}"
        
        return {
            "version": version,
            "build_time": now.strftime('%Y-%m-%d %H:%M:%S'),
            "build_timestamp": int(now.timestamp()),
            "description": "股票筛选器便携式部署包",
            "changes": [
                "支持便携式部署和一键安装",
                "集成智能更新机制",
                "优化PaddleOCR配置和性能",
                "改进用户界面和错误处理"
            ]
        }
    
    def should_exclude_file(self, file_path):
        """检查文件是否应该被排除"""
        file_str = str(file_path)
        file_name = file_path.name
        
        # 如果是目录，检查仅目录排除模式
        if file_path.is_dir():
            for pattern in self.exclude_dir_patterns:
                if file_name == pattern or pattern in file_str:
                    return True
        
        # 检查通用排除模式
        for pattern in self.exclude_patterns:
            if pattern.startswith("*") and pattern.endswith("*"):
                # 包含模式，如 *debug*
                if pattern[1:-1] in file_str:
                    return True
            elif pattern.startswith("*"):
                # 后缀模式，如 *.py
                if file_str.endswith(pattern[1:]):
                    return True
            elif pattern.endswith("*"):
                # 前缀模式，如 test_*
                if file_name.startswith(pattern[:-1]):
                    return True
            elif pattern.endswith("/"):
                # 目录模式，如 data/ - 只匹配目录
                dir_name = pattern[:-1]
                if file_path.is_dir() and (file_name == dir_name or dir_name in file_str):
                    return True
            else:
                # 精确匹配
                if file_name == pattern:
                    return True
                # 对于目录，也检查路径包含匹配
                if file_path.is_dir() and pattern in file_str:
                    return True
        
        return False
    
    def should_include_file(self, file_path):
        """检查文件是否应该被包含"""
        file_name = file_path.name
        
        # 检查包含模式
        for pattern in self.include_patterns:
            if pattern.startswith("*"):
                if file_name.endswith(pattern[1:]):
                    return True
            else:
                if file_name == pattern:
                    return True
        
        return False
    
    def collect_source_files(self):
        """收集需要打包的源码文件"""
        collected_files = []
        
        print("正在扫描源码文件...")
        for root, dirs, files in os.walk(self.source_dir):
            root_path = Path(root)
            
            # 排除特定目录
            dirs[:] = [d for d in dirs if not self.should_exclude_file(root_path / d)]
            
            for file in files:
                file_path = root_path / file
                rel_path = file_path.relative_to(self.source_dir)
                
                # 检查是否应该排除
                if self.should_exclude_file(file_path):
                    continue
                
                # 检查是否应该包含
                if self.should_include_file(file_path):
                    collected_files.append((file_path, rel_path))
                    print(f"  包含: {rel_path}")
        
        print(f"共收集到 {len(collected_files)} 个源码文件")
        return collected_files
    
    def create_deploy_structure(self, deploy_path):
        """创建部署包目录结构"""
        print("正在创建部署包目录结构...")
        
        # 创建主要目录
        src_dir = deploy_path / "src"
        src_dir.mkdir(parents=True, exist_ok=True)
        
        config_backup_dir = deploy_path / "config_backup"
        config_backup_dir.mkdir(parents=True, exist_ok=True)
        
        return src_dir, config_backup_dir
    
    def copy_source_files(self, source_files, src_dir):
        """复制源码文件到部署目录"""
        print("正在复制源码文件...")
        
        for source_path, rel_path in source_files:
            target_path = src_dir / rel_path
            target_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(source_path, target_path)
        
        print(f"已复制 {len(source_files)} 个源码文件")
    
    def create_version_file(self, deploy_path):
        """创建版本信息文件"""
        version_file = deploy_path / "version.txt"
        
        version_content = f"""# 股票筛选器版本信息
版本号: {self.version_info['version']}
构建时间: {self.version_info['build_time']}
构建时间戳: {self.version_info['build_timestamp']}
描述: {self.version_info['description']}

# 更新内容:
"""
        for change in self.version_info['changes']:
            version_content += f"- {change}\n"
        
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_content)
        
        # 同时创建JSON格式的版本信息（供程序读取）
        version_json_file = deploy_path / "version.json"
        with open(version_json_file, 'w', encoding='utf-8') as f:
            json.dump(self.version_info, f, ensure_ascii=False, indent=2)
        
        print(f"已创建版本信息文件: {self.version_info['version']}")
    
    def create_requirements_file(self, deploy_path):
        """创建或复制requirements.txt文件"""
        source_req = self.source_dir / "requirements.txt"
        target_req = deploy_path / "requirements.txt"
        
        if source_req.exists():
            shutil.copy2(source_req, target_req)
            print("已复制requirements.txt文件")
        else:
            # 创建基础的requirements.txt
            basic_requirements = """# 股票筛选器依赖包
# GUI framework - tkinter is included with Python standard library

# Windows automation
pywinauto>=0.6.8

# OCR processing
paddleocr>=3.0
paddlepaddle>=2.5.0
opencv-python>=4.8.0
Pillow>=10.0.0

# Screen capture
mss>=9.0.1

# Data processing
pandas>=2.0.0
openpyxl>=3.1.0
xlrd>=2.0.0

# System utilities
psutil>=5.9.0

# Image processing
numpy>=1.24.0

# Additional dependencies for EasyOCR (optional)
torch>=2.0.0
torchvision>=0.15.0
"""
            with open(target_req, 'w', encoding='utf-8') as f:
                f.write(basic_requirements)
            print("已创建基础requirements.txt文件")
    
    def copy_deploy_scripts(self, deploy_path):
        """复制部署相关的脚本文件到部署包根目录"""
        print("正在复制部署脚本...")
        
        # 需要复制的关键部署文件
        deploy_files = [
            "deploy.bat",                # 一键部署脚本
            "start.bat",                 # 程序启动脚本
            "install_dependencies.py",   # 依赖安装器
            "update.py",                 # 智能更新脚本
            "DEPLOY_README.md",          # 部署说明文档
        ]
        
        copied_count = 0
        missing_files = []
        
        for file_name in deploy_files:
            source_file = self.source_dir / file_name
            target_file = deploy_path / file_name
            
            if source_file.exists():
                try:
                    shutil.copy2(source_file, target_file)
                    copied_count += 1
                    print(f"  ✅ 已复制: {file_name}")
                except Exception as e:
                    print(f"  ❌ 复制失败: {file_name} - {e}")
                    missing_files.append(file_name)
            else:
                print(f"  ⚠️  文件不存在: {file_name}")
                missing_files.append(file_name)
        
        print(f"部署脚本复制完成: {copied_count}/{len(deploy_files)} 个文件")
        
        if missing_files:
            print(f"⚠️  缺少以下文件: {', '.join(missing_files)}")
            print("   请确保这些文件存在于源码目录中")
        
        return copied_count, missing_files
    
    def create_deploy_package(self, package_name=None):
        """创建完整的部署包"""
        if package_name is None:
            package_name = f"stock_screener_deploy_{self.version_info['version']}"
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        deploy_path = self.output_dir / package_name
        
        if deploy_path.exists():
            print(f"删除已存在的部署目录: {deploy_path}")
            shutil.rmtree(deploy_path)
        
        print(f"创建部署包: {package_name}")
        print("=" * 50)
        
        # 1. 收集源码文件
        source_files = self.collect_source_files()
        
        # 2. 创建目录结构
        src_dir, config_backup_dir = self.create_deploy_structure(deploy_path)
        
        # 3. 复制源码文件
        self.copy_source_files(source_files, src_dir)
        
        # 4. 创建版本信息
        self.create_version_file(deploy_path)
        
        # 5. 创建requirements文件
        self.create_requirements_file(deploy_path)
        
        # 6. 复制部署脚本
        copied_count, missing_files = self.copy_deploy_scripts(deploy_path)
        
        print("=" * 50)
        print(f"部署包创建完成: {deploy_path}")
        
        if missing_files:
            print(f"⚠️  警告: 缺少 {len(missing_files)} 个部署脚本文件")
            print("   部署包可能无法正常使用，请检查源码目录")
        else:
            print("✅ 所有部署脚本已包含，部署包完整")
        
        return deploy_path
    
    def create_zip_package(self, deploy_path):
        """创建ZIP压缩包"""
        zip_name = f"{deploy_path.name}.zip"
        zip_path = self.output_dir / zip_name
        
        print(f"正在创建ZIP压缩包: {zip_name}")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(deploy_path):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(deploy_path.parent)
                    zipf.write(file_path, arc_name)
        
        print(f"ZIP压缩包创建完成: {zip_path}")
        return zip_path

def main():
    """主函数"""
    print("股票筛选器部署包生成器")
    print("=" * 50)
    
    creator = DeployPackageCreator()
    
    # 创建部署包
    deploy_path = creator.create_deploy_package()
    
    # 询问是否创建ZIP压缩包
    create_zip = input("\n是否创建ZIP压缩包？(y/n): ").lower().strip()
    if create_zip in ['y', 'yes', '是']:
        creator.create_zip_package(deploy_path)
    
    print("\n" + "=" * 50)
    print("部署包生成完成！")
    print(f"部署包位置: {deploy_path}")
    print(f"版本信息: {creator.version_info['version']}")
    print("\n后续步骤:")
    print("1. 测试部署包完整性")
    print("2. 分发给目标用户")
    print("3. 用户运行 deploy.bat 进行一键部署")

if __name__ == "__main__":
    main()