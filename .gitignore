# 股票筛选器项目 .gitignore
# 采用精简核心代码策略，只保留必要的业务代码

# ==================== Python 相关 ====================
# 字节码缓存
__pycache__/
*.py[cod]
*$py.class

# C扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# ==================== 项目特定忽略 ====================
# 测试文件 (精简策略：不包含在git中)
test_*.py
*_test.py
tests/

# 示例和临时文件
pic/
images/
screenshots/
temp/
tmp/

# OCR和调试图片文件
debug_images_*/              # 调试图片目录（带时间戳）
debug_images_????????_??????/  # 调试图片目录（明确的日期时间戳格式：YYYYMMDD_HHMMSS）
debug_images_*[0-9]*[0-9]*/  # 调试图片目录（含数字的时间戳格式）
ocr_debug_images/           # OCR专用调试目录
fund_test_*.png             # OCR基金测试图片
fund_test_*.jpg             # OCR基金测试图片（JPG格式）
region_visualization_*.png  # 区域可视化图片
capture_test_*.png          # 截图测试图片
optimized_*.png             # 优化策略调试图片
01_original_*.png           # 原始区域调试图片
test_sample*.png            # 测试样本图片
strategy_*.png              # 策略调试图片

# 临时和缓存图片
temp_*.png                  # 临时PNG文件
temp_*.jpg                  # 临时JPG文件
temp_*.jpeg                 # 临时JPEG文件
temp_*.gif                  # 临时GIF文件
temp_*.bmp                  # 临时BMP文件
cache_*.png                 # 缓存PNG文件
cache_*.jpg                 # 缓存JPG文件
capture_*.png               # 截图文件
screenshot_*.png            # 屏幕截图文件
debug_*.png                 # 调试图片文件
test_image_*.png            # 测试图片文件
test_image_*.jpg            # 测试图片文件（JPG格式）

# 图像处理临时文件
processed_*.png             # 处理后的图片
enhanced_*.png              # 增强后的图片
filtered_*.png              # 滤波后的图片
scaled_*.png                # 缩放后的图片

# 安装脚本 (用户可自行安装依赖)
*.bat
*.sh
install.*
start.*

# 日志文件
*.log
logs/

# 数据文件
data/
*.xlsx
*.xls
*.csv
*.json
*.db
*.sqlite

# 配置备份
config_backup.py
config.backup

# ==================== IDE 和编辑器 ====================
# VSCode
.vscode/

# PyCharm
.idea/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Claude AI 配置
.claude/

# ==================== 系统文件 ====================
# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==================== 其他 ====================
# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 备份文件
*.bak
*.backup
*.old
*.orig

# 临时文件
*.tmp
*.temp

# debug_images调试图片文件夹（按日期时间戳格式）
debug_images_*/
