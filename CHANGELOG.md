# 更新日志

当前版本：**v2025.07.30.1347**

## [v2025.07.30.1347] - 2025-07-30

### 界面优化
- 重新设计"指南针数据分析"界面布局，显著提升用户体验
- 主窗口宽度从800px扩展至1200px，充分利用现代显示器的宽屏优势
- 将原来8个垂直堆叠的功能区域重组为5行紧凑布局，大幅减少界面高度
- 实现多列并排布局：OCR设置+系统设置、股票分析+信号监控+自选股管理

### 控件布局精细优化
- OCR区域设置：功能按钮与显示标签左右对齐，更加紧凑直观
- 信号监控模块：买入/卖出信号控件水平排列，节省垂直空间
- 自选股管理：移除不常用的暂停按钮，优化为开始添加+停止的简洁布局
- 系统设置：鼠标定位和自动停止功能并排显示，提高配置效率

### 用户体验提升
- 减少滚动操作需求，主要功能区域可在一屏内完整显示
- 保持所有现有功能完整性，仅优化UI布局不影响业务逻辑
- 按钮和控件排列更加合理，快速找到所需功能
- 整体界面更加现代化和专业化，符合当前UI设计趋势

---

## [v2025.07.30.1200] - 2025-07-30

### 新增功能
- 新增股票代码智能清理功能，自动从"300366 预ST"格式中提取6位数字代码"300366"
- 新增`extract_6digit_stock_code()`函数，支持处理ST、预ST、*ST、退市等各种股票后缀格式
- 支持多种股票代码格式的统一处理，确保Excel输出的一致性

### 改进
- 优化网页数据提取流程，在HTML解析阶段自动清理股票代码格式
- 增强HTML数据解析器(`html_data_parser.py`)，提高股票代码识别准确性
- 完善数据清洗机制，支持带空格、特殊标记的股票代码处理
- 提升用户体验，无需手动处理复杂的股票代码格式

### 技术改进
- 在`html_data_parser.py`的`_extract_row_data()`方法中集成股票代码清理逻辑
- 采用正则表达式`r'^(\d{6})'`精确匹配6位数字股票代码
- 添加备用清理策略，提高代码提取的可靠性
- 完善测试覆盖，验证12种不同股票代码格式的处理效果

---

## [v2025.07.29.1134] - 2025-07-29

### 新增功能
- 新增系统设置可视化界面，支持通过勾选框直接控制关键配置项
- 新增"启用鼠标定位功能"可视化开关，无需手动编辑配置文件
- 新增"15:01后自动停止信号检测"可视化开关，统一控制买入和卖出信号监控

### 改进
- 优化界面布局，系统设置区域采用并排布局，充分利用水平空间
- 简化字体配置，移除所有自定义font参数，使用系统默认字体
- 增强配置管理机制，支持实时配置保存和加载
- 提升用户体验，配置变化立即生效且持久化到配置文件

### 技术改进
- 新增`update_mouse_positioning_config()`和`update_auto_stop_config()`配置更新函数
- 完善GUI初始化流程，自动加载并同步配置状态
- 优化错误处理机制，配置保存失败时自动恢复界面状态

---

## [v2025.07.28.2303] - 2025-07-28

### 改进
- 优化GUI界面布局设计，提升用户体验
- 重新组织操作控制区域，分为股票分析、信号监控、自选股管理三个模块
- 统一按钮样式和宽度，界面更加整洁协调
- 改善各区域间距和padding，增加视觉呼吸感
- 优化表格显示行数和权重分配，提高空间利用率

---

## [v2025.07.28.1800] - 2025-07-28

### 新增功能
- 新增卖出信号监控系统 (SellSignalMonitor)
- 新增统一信号分析器 (SignalAnalyzer)，支持持仓、开仓、空仓、清仓四种信号
- 新增股票自动导入功能，支持批量添加自选股
- 新增基础信号监控架构 (BaseSignalMonitor)，为买入/卖出信号提供统一基础
- 新增清仓信号危险提醒功能

### 改进
- 重构买入信号监控系统，基于新的基础架构
- 优化钉钉通知器集成，改进通知逻辑
- GUI界面模块化重构，采用更清晰的Mixin模式
- 增强版本管理系统，添加版本显示功能
- 完善配置管理机制，提高系统稳定性
- 优化OCR识别和信号处理逻辑

---

## [v2025.07.28.1517] - 2025-07-28

### 新增功能
- 新增鼠标定位功能开关配置项 (enable_mouse_positioning)
- 支持在配置文件中控制是否启用鼠标定位功能
- 改进用户体验，提供更灵活的配置选项
- 向后兼容，默认启用鼠标定位功能

### 改进
- 增强了鼠标定位系统的可配置性
- 优化了配置管理机制

---

## [v2025.01.01.0000] - 2025-01-01

### 新增功能
- 支持便携式部署和一键安装
- 集成智能更新机制
- 实现买入信号发给钉钉机器人
- 添加买入信号的识别功能
- 增加鼠标坐标设置功能，添加pyautogui组件

### 改进
- 优化PaddleOCR配置和性能
- 改进用户界面和错误处理
- 优化OCR识别准确率
- 完善错误处理和用户体验

### 新增
- 创建自动化部署和更新机制
- 增加是否开启鼠标定位功能的配置

---

## 版本号格式说明

版本号格式：`vYYYY.MM.DD.HHMM`

- `YYYY`：年份
- `MM`：月份（01-12）
- `DD`：日期（01-31）
- `HHMM`：时间（24小时制，精确到分钟）

## 更新指南

1. 修改此文档顶部的当前版本号
2. 在对应版本段落添加详细的更新内容
3. 按照时间倒序排列版本记录
4. 使用标准的语义化版本标签：
   - **新增功能**：全新的功能特性
   - **改进**：对现有功能的增强
   - **修复**：错误修复
   - **变更**：可能影响兼容性的变更
   - **移除**：已移除的功能