# -*- coding: utf-8 -*-
"""
GUI 股票自动输入操作模块
实现批量添加自选股的键盘输入功能
"""

import threading
import time
import tkinter as tk
from tkinter import messagebox
import keyboard
import pya<PERSON>gu<PERSON>
from typing import List, Optional

from config import APP_CONFIG


class GUIStockImportOperationsMixin:
    """GUI 股票自动输入操作Mixin类"""
    
    def init_stock_import_components(self):
        """初始化股票输入相关组件"""
        # 股票输入相关状态变量
        self.stock_import_active = False
        self.stock_import_thread = None
        self.stock_import_paused = False
        self.current_stock_list = []
        self.current_stock_index = 0
        self.total_stocks_count = 0
        
        # 倒计时相关变量
        self.countdown_active = False
        self.countdown_seconds = 10
        
    def start_stock_import_process(self):
        """开始股票自动输入流程"""
        try:
            if self.stock_import_active:
                messagebox.showwarning("提示", "股票自动输入正在进行中")
                return
            
            # 检查是否选择了Excel文件
            if not self.current_excel_path:
                messagebox.showerror("错误", "请先选择包含股票代码的Excel文件")
                return
            
            # 从Excel文件读取股票代码列表
            try:
                stock_codes = self.data_processor.load_excel_file(self.current_excel_path)
                if not stock_codes:
                    messagebox.showerror("错误", "Excel文件中没有找到有效的股票代码")
                    return
                
                self.current_stock_list = stock_codes
                self.total_stocks_count = len(stock_codes)
                self.current_stock_index = 0
                
                self.logger.info(f"成功读取 {self.total_stocks_count} 个股票代码")
                
            except Exception as e:
                self.logger.error(f"读取Excel文件失败: {str(e)}")
                messagebox.showerror("错误", f"读取Excel文件失败: {str(e)}")
                return
            
            # 确认开始流程
            result = messagebox.askyesno(
                "确认开始", 
                f"将要自动输入 {self.total_stocks_count} 个股票代码。\n\n"
                f"请确保：\n"
                f"1. 已经打开股票软件并定位到股票输入界面\n"
                f"2. 输入焦点在正确的输入框内\n\n"
                f"点击'是'后将有10秒准备时间，确认开始吗？"
            )
            
            if not result:
                return
            
            # 设置状态
            self.stock_import_active = True
            self.stock_import_paused = False
            
            # 更新UI状态
            self._update_stock_import_ui_state()
            
            # 开始倒计时和自动输入线程
            self.stock_import_thread = threading.Thread(
                target=self._stock_import_thread_worker, 
                daemon=True
            )
            self.stock_import_thread.start()
            
            self.logger.info("股票自动输入流程已启动")
            
        except Exception as e:
            self.logger.error(f"启动股票自动输入失败: {str(e)}")
            messagebox.showerror("错误", f"启动失败: {str(e)}")
            self._stop_stock_import_internal()
    
    def _stock_import_thread_worker(self):
        """股票自动输入工作线程"""
        try:
            # 第一阶段：倒计时
            self._run_countdown()
            
            if not self.stock_import_active:
                return
            
            # 第二阶段：自动输入股票代码
            self._run_stock_input_loop()
            
        except Exception as e:
            self.logger.error(f"股票自动输入线程错误: {str(e)}")
            self.message_queue.put(("log", f"自动输入发生错误: {str(e)}"))
        finally:
            # 清理状态
            self._stop_stock_import_internal()
    
    def _run_countdown(self):
        """运行倒计时"""
        try:
            self.countdown_active = True
            self.countdown_seconds = 10
            
            self.message_queue.put(("log", "开始10秒准备倒计时..."))
            
            while self.countdown_seconds > 0 and self.stock_import_active:
                # 更新UI显示倒计时
                def update_countdown():
                    if hasattr(self, 'stock_import_status_var'):
                        self.stock_import_status_var.set(f"准备中... {self.countdown_seconds}秒")
                
                if hasattr(self, 'root'):
                    self.root.after(0, update_countdown)
                
                time.sleep(1)
                self.countdown_seconds -= 1
            
            self.countdown_active = False
            
            if self.stock_import_active:
                self.message_queue.put(("log", "倒计时结束，开始自动输入股票代码"))
                
        except Exception as e:
            self.logger.error(f"倒计时过程出错: {str(e)}")
    
    def _run_stock_input_loop(self):
        """运行股票输入循环"""
        try:
            for i, stock_code in enumerate(self.current_stock_list):
                if not self.stock_import_active:
                    break
                
                # 处理暂停状态
                while self.stock_import_paused and self.stock_import_active:
                    time.sleep(0.1)
                
                if not self.stock_import_active:
                    break
                
                self.current_stock_index = i
                
                # 更新UI状态
                def update_progress():
                    if hasattr(self, 'stock_import_status_var'):
                        progress = (i + 1) / self.total_stocks_count * 100
                        self.stock_import_status_var.set(
                            f"正在输入: {stock_code} ({i + 1}/{self.total_stocks_count}) {progress:.1f}%"
                        )
                    if hasattr(self, 'stock_import_progress_var'):
                        self.stock_import_progress_var.set((i + 1) / self.total_stocks_count * 100)
                
                if hasattr(self, 'root'):
                    self.root.after(0, update_progress)
                
                # 执行键盘输入操作
                success = self._input_single_stock_code(stock_code)
                
                if success:
                    self.message_queue.put(("log", f"已输入股票代码: {stock_code}"))
                else:
                    self.message_queue.put(("log", f"输入股票代码失败: {stock_code}"))
                
                # 短暂延时，避免输入过快
                time.sleep(0.3)
            
            # 完成所有输入
            if self.stock_import_active:
                self.message_queue.put(("log", f"股票代码输入完成！共处理 {len(self.current_stock_list)} 个股票"))
                
                def show_completion():
                    messagebox.showinfo("完成", f"股票代码输入完成！\n共处理 {len(self.current_stock_list)} 个股票")
                
                if hasattr(self, 'root'):
                    self.root.after(0, show_completion)
                
        except Exception as e:
            self.logger.error(f"股票输入循环出错: {str(e)}")
            self.message_queue.put(("log", f"股票输入过程发生错误: {str(e)}"))
    
    def _input_single_stock_code(self, stock_code: str) -> bool:
        """
        输入单个股票代码
        
        Args:
            stock_code: 股票代码
            
        Returns:
            是否输入成功
        """
        try:
            # 清理股票代码，移除可能的空格和特殊字符
            cleaned_code = str(stock_code).strip()
            
            if not cleaned_code:
                self.logger.warning(f"股票代码为空，跳过")
                return False
            
            # 输入股票代码
            keyboard.write(cleaned_code)
            time.sleep(0.2)  # 短暂等待确保输入完成
            
            # 按回车键
            keyboard.press_and_release('enter')
            time.sleep(0.5)  # 等待系统响应
            
            # 按ESC键清空输入框
            keyboard.press_and_release('esc')
            time.sleep(0.3)  # 等待清空完成
            
            return True
            
        except Exception as e:
            self.logger.error(f"输入股票代码 {stock_code} 失败: {str(e)}")
            return False
    
    def pause_stock_import(self):
        """暂停股票自动输入"""
        try:
            if not self.stock_import_active:
                return
            
            self.stock_import_paused = True
            self.message_queue.put(("log", "股票自动输入已暂停"))
            
            # 更新UI状态
            self._update_stock_import_ui_state()
            
        except Exception as e:
            self.logger.error(f"暂停股票自动输入失败: {str(e)}")
    
    def resume_stock_import(self):
        """恢复股票自动输入"""
        try:
            if not self.stock_import_active or not self.stock_import_paused:
                return
            
            self.stock_import_paused = False
            self.message_queue.put(("log", "股票自动输入已恢复"))
            
            # 更新UI状态
            self._update_stock_import_ui_state()
            
        except Exception as e:
            self.logger.error(f"恢复股票自动输入失败: {str(e)}")
    
    def stop_stock_import(self):
        """停止股票自动输入"""
        try:
            if not self.stock_import_active:
                return
            
            # 询问确认
            result = messagebox.askyesno("确认停止", "确定要停止股票自动输入吗？")
            if not result:
                return
            
            self._stop_stock_import_internal()
            self.message_queue.put(("log", "股票自动输入已停止"))
            
        except Exception as e:
            self.logger.error(f"停止股票自动输入失败: {str(e)}")
    
    def _stop_stock_import_internal(self):
        """内部停止股票自动输入（不显示确认对话框）"""
        try:
            self.stock_import_active = False
            self.stock_import_paused = False
            self.countdown_active = False
            
            # 更新UI状态
            def update_ui():
                self._update_stock_import_ui_state()
                if hasattr(self, 'stock_import_status_var'):
                    self.stock_import_status_var.set("未开始")
                if hasattr(self, 'stock_import_progress_var'):
                    self.stock_import_progress_var.set(0)
            
            if hasattr(self, 'root'):
                self.root.after(0, update_ui)
            
        except Exception as e:
            self.logger.error(f"内部停止股票自动输入失败: {str(e)}")
    
    def _update_stock_import_ui_state(self):
        """更新股票自动输入相关UI状态"""
        try:
            if hasattr(self, 'stock_import_start_btn'):
                if self.stock_import_active:
                    self.stock_import_start_btn.config(state='disabled')
                else:
                    self.stock_import_start_btn.config(state='normal')
            
            if hasattr(self, 'stock_import_pause_btn'):
                if self.stock_import_active and not self.stock_import_paused:
                    self.stock_import_pause_btn.config(state='normal', text="暂停输入")
                elif self.stock_import_active and self.stock_import_paused:
                    self.stock_import_pause_btn.config(state='normal', text="恢复输入")
                else:
                    self.stock_import_pause_btn.config(state='disabled', text="暂停输入")
            
            if hasattr(self, 'stock_import_stop_btn'):
                if self.stock_import_active:
                    self.stock_import_stop_btn.config(state='normal')
                else:
                    self.stock_import_stop_btn.config(state='disabled')
                    
        except Exception as e:
            self.logger.error(f"更新股票自动输入UI状态失败: {str(e)}")
    
    def _on_stock_import_pause_click(self):
        """处理暂停/恢复按钮点击"""
        try:
            if self.stock_import_paused:
                self.resume_stock_import()
            else:
                self.pause_stock_import()
        except Exception as e:
            self.logger.error(f"处理暂停/恢复按钮点击失败: {str(e)}")
    
    def get_stock_import_status_info(self) -> str:
        """获取股票自动输入状态信息"""
        try:
            if not self.stock_import_active:
                return "未开始"
            elif self.countdown_active:
                return f"准备中... {self.countdown_seconds}秒"
            elif self.stock_import_paused:
                return f"已暂停 ({self.current_stock_index + 1}/{self.total_stocks_count})"
            else:
                progress = (self.current_stock_index + 1) / self.total_stocks_count * 100
                return f"输入中... ({self.current_stock_index + 1}/{self.total_stocks_count}) {progress:.1f}%"
        except Exception:
            return "状态未知"