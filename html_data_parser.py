#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML数据解析器 - 基于表头智能定位的股票数据提取器

该模块实现了智能的HTML数据提取功能，通过解析表头结构自动确定目标列位置，
然后精确提取股票的各项技术指标数据。

主要功能：
1. 智能表头解析和列映射
2. 股票基本信息提取（代码、名称）
3. 技术指标提取（连板接力、竞王、红盘起爆、绿盘低吸）
4. 数据清洗和格式化
5. 错误处理和容错机制

创建时间: 2025-01-29
作者: Claude Code
"""

import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from bs4 import BeautifulSoup, Tag
import pandas as pd

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_6digit_stock_code(code_text: str) -> str:
    """
    从股票代码文本中提取6位数字部分
    
    处理格式如："300366 预ST" → "300366"
    
    Args:
        code_text: 原始股票代码文本
        
    Returns:
        6位数字股票代码，如果无法提取则返回原文本
        
    Examples:
        >>> extract_6digit_stock_code("300366 预ST")
        "300366"
        >>> extract_6digit_stock_code("000001 ST")
        "000001"
        >>> extract_6digit_stock_code("600000")
        "600000"
    """
    if not code_text:
        return ""
    
    # 去除首尾空格
    clean_text = str(code_text).strip()
    
    if not clean_text:
        return ""
    
    # 使用正则表达式匹配开头的6位数字
    match = re.match(r'^(\d{6})', clean_text)
    
    if match:
        extracted_code = match.group(1)
        logger.debug(f"股票代码清理: '{code_text}' → '{extracted_code}'")
        return extracted_code
    
    # 如果没有匹配到6位数字开头，检查是否本身就是6位数字
    if re.match(r'^\d{6}$', clean_text):
        return clean_text
    
    # 备用方案：尝试从空格分割的第一部分提取
    first_part = clean_text.split()[0] if ' ' in clean_text else clean_text
    if re.match(r'^\d{6}$', first_part):
        logger.debug(f"股票代码清理(备用): '{code_text}' → '{first_part}'")
        return first_part
    
    # 如果都无法提取，记录警告并返回原文本
    logger.warning(f"无法从 '{code_text}' 中提取6位股票代码，保持原样")
    return clean_text


class HTMLDataParser:
    """
    HTML数据解析器类
    
    用于从股票网页中智能提取数据，支持基于表头的列定位和数据清洗。
    """
    
    def __init__(self):
        """初始化解析器"""
        self.column_mapping = {}
        self.target_columns = {
            'stock_code': '股票代码',
            'stock_name': '股票名称',
            'lianban_jieli': '小草连板接力',
            'jingwang': '小草竞王',
            'hongpan_qibao': '小草红盘起爆',
            'lvpan_dixi': '小草绿盘低吸'
        }
        
    def parse_html(self, html_content: str) -> BeautifulSoup:
        """
        解析HTML内容
        
        Args:
            html_content: HTML字符串内容
            
        Returns:
            BeautifulSoup对象
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            return soup
        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
            raise
            
    def parse_table_headers(self, soup: BeautifulSoup) -> Dict[str, int]:
        """
        解析表头，建立列名到索引的映射
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            列名到索引的映射字典
        """
        try:
            # 查找表头行
            header_row = soup.select('.c-table-row-head')
            if not header_row:
                raise ValueError("未找到表头行")
                
            header_cells = header_row[0].select('.c-table-cell')
            if not header_cells:
                raise ValueError("表头中未找到列")
                
            column_mapping = {}
            for index, cell in enumerate(header_cells):
                # 提取列名文本
                text = self._extract_cell_text(cell).strip()
                if text:
                    column_mapping[text] = index
                    
            # 验证必需的列是否存在
            missing_columns = []
            for key, column_name in self.target_columns.items():
                if column_name not in column_mapping:
                    missing_columns.append(column_name)
                    
            if missing_columns:
                logger.warning(f"缺少以下列: {missing_columns}")
                
            self.column_mapping = column_mapping
            logger.info(f"成功解析表头，共{len(column_mapping)}列")
            return column_mapping
            
        except Exception as e:
            logger.error(f"表头解析失败: {e}")
            raise
            
    def _extract_cell_text(self, cell: Tag) -> str:
        """
        从表格单元格中提取纯文本
        
        Args:
            cell: BeautifulSoup Tag对象
            
        Returns:
            清理后的文本内容
        """
        # 获取所有文本内容
        text = cell.get_text(separator=' ', strip=True)
        
        # 清理多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        return text
        
    def extract_stock_data(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        提取所有股票数据
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            股票数据列表
        """
        try:
            # 如果还没有解析表头，先解析
            if not self.column_mapping:
                self.parse_table_headers(soup)
                
            # 获取所有数据行
            data_rows = soup.select('.c-table-row-body')
            if not data_rows:
                raise ValueError("未找到数据行")
                
            stock_data_list = []
            
            for row_index, row in enumerate(data_rows):
                try:
                    stock_data = self._extract_row_data(row)
                    if stock_data:
                        stock_data_list.append(stock_data)
                except Exception as e:
                    logger.warning(f"第{row_index + 1}行数据提取失败: {e}")
                    continue
                    
            logger.info(f"成功提取{len(stock_data_list)}只股票的数据")
            return stock_data_list
            
        except Exception as e:
            logger.error(f"股票数据提取失败: {e}")
            raise
            
    def _extract_row_data(self, row: Tag) -> Optional[Dict[str, Any]]:
        """
        从单行中提取股票数据
        
        Args:
            row: 表格行Tag对象
            
        Returns:
            股票数据字典
        """
        try:
            # 获取所有单元格
            cells = row.select('.c-table-cell')
            if len(cells) < max(self.column_mapping.values()) + 1:
                logger.warning("行中的列数不足")
                return None
                
            stock_data = {}
            
            # 提取目标列的数据
            for key, column_name in self.target_columns.items():
                if column_name in self.column_mapping:
                    column_index = self.column_mapping[column_name]
                    if column_index < len(cells):
                        cell_value = self._extract_and_clean_value(cells[column_index])
                        # 如果是股票代码字段，应用6位数字提取清理
                        if key == 'stock_code':
                            cell_value = extract_6digit_stock_code(cell_value)
                        stock_data[key] = cell_value
                    else:
                        stock_data[key] = ""
                else:
                    stock_data[key] = ""
                    
            # 验证必要字段（增强版调试信息）
            stock_code = stock_data.get('stock_code', '').strip()
            stock_name = stock_data.get('stock_name', '').strip()
            
            if not stock_code or not stock_name:
                # 收集详细的调试信息
                debug_info = {
                    'stock_code': f"'{stock_code}'" if stock_code else "空",
                    'stock_name': f"'{stock_name}'" if stock_name else "空",
                    'cells_count': len(cells),
                    'expected_columns': len(self.column_mapping),
                    'row_html_snippet': str(row)[:200] + "..." if len(str(row)) > 200 else str(row)
                }
                
                # 检查是否是完全空白的行（可能是虚拟滚动渲染问题）
                all_values = [stock_data.get(key, '').strip() for key in stock_data.keys()]
                non_empty_values = [v for v in all_values if v]
                
                if len(non_empty_values) == 0:
                    logger.debug(f"跳过完全空白行（可能是虚拟滚动渲染问题）: {debug_info}")
                else:
                    logger.warning(f"股票代码或名称为空，但其他字段有数据: {debug_info}, 非空字段数: {len(non_empty_values)}")
                
                return None
                
            return stock_data
            
        except Exception as e:
            logger.error(f"行数据提取失败: {e}")
            return None
            
    def _extract_and_clean_value(self, cell: Tag) -> str:
        """
        提取并清理单元格值
        
        Args:
            cell: 单元格Tag对象
            
        Returns:
            清理后的值
        """
        # 提取文本内容
        text = self._extract_cell_text(cell)
        
        # 清理数值格式
        # 移除常见的格式符号但保留原始数值结构
        cleaned_text = text.strip()
        
        # 如果是数值格式，保持原样
        if re.match(r'^[+\-]?\d+\.?\d*[%亿万]?$', cleaned_text):
            return cleaned_text
            
        return cleaned_text
        
    def validate_data_structure(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """
        验证网页数据结构
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            验证结果字典
        """
        result = {
            'is_valid': False,
            'header_found': False,
            'data_rows_found': False,
            'target_columns_found': [],
            'missing_columns': [],
            'total_rows': 0,
            'total_columns': 0
        }
        
        try:
            # 检查表头
            header_rows = soup.select('.c-table-row-head')
            if header_rows:
                result['header_found'] = True
                
                # 解析表头
                column_mapping = self.parse_table_headers(soup)
                result['total_columns'] = len(column_mapping)
                
                # 检查目标列
                for key, column_name in self.target_columns.items():
                    if column_name in column_mapping:
                        result['target_columns_found'].append(column_name)
                    else:
                        result['missing_columns'].append(column_name)
                        
            # 检查数据行
            data_rows = soup.select('.c-table-row-body')
            if data_rows:
                result['data_rows_found'] = True
                result['total_rows'] = len(data_rows)
                
            # 综合判断是否有效
            result['is_valid'] = (
                result['header_found'] and 
                result['data_rows_found'] and 
                len(result['missing_columns']) == 0
            )
            
        except Exception as e:
            logger.error(f"数据结构验证失败: {e}")
            result['error'] = str(e)
            
        return result
        
    def export_to_dataframe(self, stock_data_list: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        将股票数据导出为DataFrame
        
        Args:
            stock_data_list: 股票数据列表
            
        Returns:
            pandas DataFrame
        """
        if not stock_data_list:
            return pd.DataFrame()
            
        # 创建DataFrame
        df = pd.DataFrame(stock_data_list)
        
        # 重命名列为中文
        column_rename = {
            'stock_code': '股票代码',
            'stock_name': '股票名称',
            'lianban_jieli': '连板接力',
            'jingwang': '竞王',
            'hongpan_qibao': '红盘起爆',
            'lvpan_dixi': '绿盘低吸'
        }
        
        df = df.rename(columns=column_rename)
        
        # 调整列顺序
        ordered_columns = ['股票代码', '股票名称', '连板接力', '竞王', '红盘起爆', '绿盘低吸']
        df = df[ordered_columns]
        
        return df
        
    def export_to_excel(self, stock_data_list: List[Dict[str, Any]], 
                       file_path: str) -> bool:
        """
        将股票数据导出为Excel文件
        
        Args:
            stock_data_list: 股票数据列表
            file_path: 输出文件路径
            
        Returns:
            是否成功
        """
        try:
            df = self.export_to_dataframe(stock_data_list)
            if df.empty:
                logger.warning("没有数据可导出")
                return False
                
            df.to_excel(file_path, index=False, engine='openpyxl')
            logger.info(f"数据已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Excel导出失败: {e}")
            return False


class HTMLDataParserManager:
    """HTML数据解析器管理器"""
    
    def __init__(self):
        self.parser = HTMLDataParser()
        
    def parse_stock_data_from_html(self, html_content: str) -> List[Dict[str, Any]]:
        """
        从HTML内容中解析股票数据
        
        Args:
            html_content: HTML字符串内容
            
        Returns:
            股票数据列表
        """
        try:
            soup = self.parser.parse_html(html_content)
            return self.parser.extract_stock_data(soup)
        except Exception as e:
            logger.error(f"HTML数据解析失败: {e}")
            return []
            
    def validate_html_structure(self, html_content: str) -> Dict[str, Any]:
        """
        验证HTML结构
        
        Args:
            html_content: HTML字符串内容
            
        Returns:
            验证结果字典
        """
        try:
            soup = self.parser.parse_html(html_content)
            return self.parser.validate_data_structure(soup)
        except Exception as e:
            logger.error(f"HTML结构验证失败: {e}")
            return {'is_valid': False, 'error': str(e)}
            
    def export_to_excel(self, html_content: str, file_path: str) -> bool:
        """
        直接从HTML导出到Excel
        
        Args:
            html_content: HTML字符串内容
            file_path: 输出文件路径
            
        Returns:
            是否成功
        """
        try:
            stock_data = self.parse_stock_data_from_html(html_content)
            if not stock_data:
                logger.warning("没有提取到股票数据")
                return False
                
            return self.parser.export_to_excel(stock_data, file_path)
        except Exception as e:
            logger.error(f"HTML到Excel导出失败: {e}")
            return False


# 工厂函数
def create_html_data_parser() -> HTMLDataParser:
    """
    创建HTML数据解析器实例
    
    Returns:
        HTMLDataParser实例
    """
    return HTMLDataParser()


def create_html_data_parser_manager() -> HTMLDataParserManager:
    """
    创建HTML数据解析器管理器实例
    
    Returns:
        HTMLDataParserManager实例
    """
    return HTMLDataParserManager()


# 便捷函数
def extract_stock_data_from_html(html_content: str) -> List[Dict[str, Any]]:
    """
    从HTML内容中提取股票数据（便捷函数）
    
    Args:
        html_content: HTML字符串内容
        
    Returns:
        股票数据列表
    """
    manager = create_html_data_parser_manager()
    return manager.parse_stock_data_from_html(html_content)


def validate_html_structure(html_content: str) -> Dict[str, Any]:
    """
    验证HTML结构（便捷函数）
    
    Args:
        html_content: HTML字符串内容
        
    Returns:
        验证结果字典
    """
    manager = create_html_data_parser_manager()
    return manager.validate_html_structure(html_content)


def export_html_to_excel(html_content: str, file_path: str) -> bool:
    """
    从HTML导出到Excel（便捷函数）
    
    Args:
        html_content: HTML字符串内容
        file_path: 输出文件路径
        
    Returns:
        是否成功
    """
    manager = create_html_data_parser_manager()
    return manager.export_to_excel(html_content, file_path)


if __name__ == "__main__":
    # 测试代码
    print("HTML数据解析器模块 - 测试模式")
    
    # 测试股票代码清理功能
    print("\n=== 股票代码清理功能测试 ===")
    test_cases = [
        "300366 预ST",      # 标准格式：6位数字 + 空格 + 文字
        "000001 ST",        # ST股票
        "600000 退市",      # 退市股票 
        "300032",           # 纯6位数字（无后缀）
        "002415 N",         # N开头新股
        "000858 *ST",       # *ST股票
        "123456 预ST测试",   # 带更多文字
        "   300366 预ST  ", # 带前后空格
        "",                 # 空字符串
        "12345",            # 不足6位
        "1234567",          # 超过6位
        "ABC123",           # 非数字开头
    ]
    
    for test_code in test_cases:
        cleaned = extract_6digit_stock_code(test_code)
        print(f"'{test_code:15}' → '{cleaned}'")
    
    print("\n=== HTML数据解析测试 ===")
    
    # 构造测试HTML数据（修改股票代码为带后缀的格式）
    test_html = """
    <div class="c-table-row c-table-row-head" style="width: 2350px;">
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-center bd-r" style="width: 46px; left: 0px; cursor: default;">排序</div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-center bd-r" style="width: 102px; left: 46px; cursor: default;">全部</div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-left bd-r" style="width: 134px; left: 148px; cursor: default;">股票代码</div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-left bd-r" style="width: 102px; left: 282px; cursor: default;">股票名称</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px; cursor: pointer;">开幅</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px; cursor: pointer;">涨幅</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px; cursor: pointer;">当日盈亏</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px; cursor: pointer;">昨日连板</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px; cursor: pointer;">今日连板</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 126px; cursor: pointer;">流通市值</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px; cursor: pointer;">小草连板接力</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px; cursor: pointer;">小草竞王</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px; cursor: pointer;">小草红盘起爆</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px; cursor: pointer;">小草绿盘低吸</div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 312px; cursor: default;">小草方向</div>
    </div>
    <div class="c-table-row c-table-row-body" style="width: 2350px; cursor: pointer;">
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-center bd-r" style="width: 46px; left: 0px;">4</div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-center bd-r" style="width: 102px; left: 46px;">
            <div class="c-xiaocao-stock-tag">
                <span style="--color: #34E8E9;">强度增强</span>
                <span style="--color: #FFC7DC;">强度中</span>
                <span style="--color: #34E8E9;">低位</span>
            </div>
        </div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-left bd-r" style="width: 134px; left: 148px;">
            <span style="color: rgb(255, 168, 85);">300032 预ST</span>
        </div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-left bd-r" style="width: 102px; left: 282px;">
            <span style="color: rgb(255, 168, 85);">金龙机电</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0.00%</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <span class="c-format color-down" style="color: rgb(16, 204, 85); --color: #10CC55;">-0.73%</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <div class="c-format color-down" style="font-weight: 500; width: 100%; height: 100%; padding-left: 16px; position: relative; left: -16px; background: rgba(255, 61, 61, 0.2); color: rgb(16, 204, 85); --color: #10CC55;">-0.73%</div>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 126px;">
            <span class="c-format color-up" style="color: rgb(255, 61, 61); --color: #FF3D3D;">43.53亿</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0.00</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px;">
            <span class="c-format color-up" style="color: rgb(255, 61, 61); --color: #FF3D3D;">11.20</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0.00</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0.00</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 312px;">
            <div class="ellipsis">
                <span class="c-xiaocao-rank c-xiaocao-customcolor-duanxian-000038-BKDL-1 c-format" style="padding-right: 10px;">**深股通</span>
                <span class="c-xiaocao-rank c-xiaocao-customcolor-duanxian-000027-BKDL-1 c-format" style="padding-right: 10px;">新能源汽车</span>
            </div>
        </div>
    </div>
    <div class="c-table-row c-table-row-body" style="width: 2350px; cursor: pointer;">
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-center bd-r" style="width: 46px; left: 0px;">3</div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-center bd-r" style="width: 102px; left: 46px;">
            <div class="c-xiaocao-stock-tag">
                <span style="--color: #98D7B6;">强度减弱</span>
                <span style="--color: #4488DB;">强度低</span>
                <span style="--color: #34E8E9;">小低开</span>
                <span style="--color: #34E8E9;">低位</span>
            </div>
        </div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-left bd-r" style="width: 134px; left: 148px;">
            <span style="color: rgb(255, 168, 85);">300031 ST</span>
        </div>
        <div class="c-table-cell c-table-cell-fixed-left c-table-cell-algin-left bd-r" style="width: 102px; left: 282px;">
            <span style="color: rgb(255, 168, 85);">宝通科技</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <span class="c-format color-down" style="color: rgb(16, 204, 85); --color: #10CC55;">-0.45%</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <span class="c-format color-up" style="color: rgb(255, 61, 61); --color: #FF3D3D;">*****%</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <div class="c-format color-up" style="font-weight: 500; width: 100%; height: 100%; padding-left: 16px; position: relative; left: -16px; background: rgba(255, 61, 61, 0.2); color: rgb(255, 61, 61); --color: #FF3D3D;">*****%</div>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 88px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 126px;">
            <span class="c-format color-up" style="color: rgb(255, 61, 61); --color: #FF3D3D;">41.23亿</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0.00</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px;">
            <span class="c-format color-up" style="color: rgb(255, 61, 61); --color: #FF3D3D;">8.90</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0.00</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 116px;">
            <span class="c-format" style="color: rgb(227, 227, 227); --color: #E3E3E3;">0.00</span>
        </div>
        <div class="c-table-cell c-table-cell-algin-left bd-r" style="width: 312px;">
            <div class="ellipsis">
                <span class="c-xiaocao-rank c-xiaocao-customcolor-duanxian-000038-BKDL-1 c-format" style="padding-right: 10px;">**深股通</span>
                <span class="c-xiaocao-rank c-xiaocao-customcolor-duanxian-000027-BKDL-1 c-format" style="padding-right: 10px;">新能源汽车</span>
            </div>
        </div>
    </div>
    """
    
    try:
        # 验证HTML结构
        result = validate_html_structure(test_html)
        print(f"验证结果: {result}")
        
        if result['is_valid']:
            # 提取股票数据
            stock_data = extract_stock_data_from_html(test_html)
            print(f"提取到的数据: {stock_data}")
            
            # 验证股票代码清理效果
            print("\n=== 股票代码清理效果验证 ===")
            for i, stock in enumerate(stock_data):
                print(f"股票 {i+1}: 代码='{stock.get('stock_code', '')}', 名称='{stock.get('stock_name', '')}'")
            
            # 导出到Excel（仅测试，实际使用时需要提供真实路径）
            # export_html_to_excel(test_html, "test_output.xlsx")
            
        else:
            print(f"HTML结构验证失败: {result}")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()