# -*- coding: utf-8 -*-
"""
优化的虚拟滚动处理模块
简化逻辑，提高可靠性，专门处理c-virtual-group虚拟滚动
"""

import asyncio
import logging
from typing import Tuple, Callable, Optional

class OptimizedVirtualScrollHandler:
    """优化的虚拟滚动处理器"""
    
    def __init__(self, page, logger=None):
        self.page = page
        self.logger = logger or logging.getLogger(__name__)
        
        # 加载配置
        try:
            from config import WEB_AUTOMATION_CONFIG
            self.config = WEB_AUTOMATION_CONFIG
            
            # 获取滚动策略配置
            scroll_strategy = self.config.get('scroll_loading', {}).get('scroll_strategy', {})
            self.scroll_wait_time = scroll_strategy.get('scroll_wait_time', 4.0)
            self.data_change_wait = scroll_strategy.get('data_change_wait', 3.0)
            self.scroll_distance = scroll_strategy.get('scroll_distance', 400)
            
            self.logger.info(f"虚拟滚动配置加载: 等待时间={self.scroll_wait_time}s, 数据变化等待={self.data_change_wait}s")
        except ImportError:
            # 如果无法导入配置，使用默认值
            self.logger.warning("无法加载配置，使用默认虚拟滚动参数")
            self.scroll_wait_time = 4.0
            self.data_change_wait = 3.0
            self.scroll_distance = 400
        
    async def scroll_and_load_all_data(self, progress_callback: Optional[Callable] = None) -> Tuple[bool, str, int]:
        """
        优化的滚动加载所有数据
        专门针对c-virtual-group虚拟滚动进行优化
        """
        try:
            self.logger.info("🚀 开始优化版虚拟滚动加载...")
            
            # 1. 首先检测虚拟滚动结构
            scroll_info = await self._detect_and_analyze_virtual_scroll()
            
            if not scroll_info['is_virtual']:
                return await self._fallback_to_normal_scroll(progress_callback)
            
            self.logger.info(f"✅ 检测到虚拟滚动: {scroll_info['description']}")
            
            # 2. 执行优化的虚拟滚动
            return await self._execute_optimized_virtual_scroll(scroll_info, progress_callback)
            
        except Exception as e:
            error_msg = f"优化滚动加载失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, 0
    
    async def _detect_and_analyze_virtual_scroll(self) -> dict:
        """检测并分析虚拟滚动结构"""
        
        detection_script = """
        () => {
            // 增强的虚拟滚动检测
            function detectVirtualScrollStructure() {
                // 优先检测 c-virtual-group 结构
                const virtualGroup = document.querySelector('.c-virtual-group');
                const virtualBefore = document.querySelector('.c-virtual-before');
                
                if (virtualGroup && virtualBefore) {
                    const groupRect = virtualGroup.getBoundingClientRect();
                    const beforeStyle = window.getComputedStyle(virtualBefore);
                    
                    // 获取初始transform值
                    let initialOffset = 0;
                    const transform = beforeStyle.transform;
                    if (transform && transform !== 'none') {
                        const translateYMatch = transform.match(/translateY\\(([^)]+)\\)/);
                        const matrixMatch = transform.match(/matrix\\([^,]+,[^,]+,[^,]+,[^,]+,[^,]+,([^)]+)\\)/);
                        
                        if (translateYMatch) {
                            initialOffset = Math.abs(parseFloat(translateYMatch[1].replace('px', '')));
                        } else if (matrixMatch) {
                            initialOffset = Math.abs(parseFloat(matrixMatch[1]));
                        }
                    }
                    
                    return {
                        found: true,
                        type: 'c-virtual-group',
                        groupElement: virtualGroup,
                        beforeElement: virtualBefore,
                        initialOffset: initialOffset,
                        groupHeight: groupRect.height || virtualGroup.offsetHeight,
                        scrollContainer: virtualGroup.closest('[style*="overflow"]') || virtualGroup
                    };
                }
                
                // 备用检测：查找其他虚拟滚动模式
                const candidateElements = [
                    { selector: '.virtual-group', beforeSelector: '.virtual-before' },
                    { selector: '[class*="virtual-list"]', beforeSelector: '[class*="virtual"][class*="before"], [class*="offset"]' },
                    { selector: '.ant-virtual-list', beforeSelector: '.ant-virtual-list-holder-inner' }
                ];
                
                for (const candidate of candidateElements) {
                    const group = document.querySelector(candidate.selector);
                    const before = document.querySelector(candidate.beforeSelector);
                    
                    if (group && before) {
                        return {
                            found: true,
                            type: candidate.selector,
                            groupElement: group,
                            beforeElement: before,
                            initialOffset: 0,
                            groupHeight: group.getBoundingClientRect().height || group.offsetHeight,
                            scrollContainer: group
                        };
                    }
                }
                
                return { found: false };
            }
            
            const detection = detectVirtualScrollStructure();
            
            if (!detection.found) {
                return {
                    is_virtual: false,
                    description: '未检测到虚拟滚动结构'
                };
            }
            
            // 收集虚拟滚动详细信息
            const groupRect = detection.groupElement.getBoundingClientRect();
            const beforeRect = detection.beforeElement.getBoundingClientRect();
            const dataRows = document.querySelectorAll('.c-table-row-body, [class*="row"], tr').length;
            
            return {
                is_virtual: true,
                type: detection.type,
                description: `虚拟滚动类型: ${detection.type}`,
                group_height: detection.groupHeight,
                initial_offset: detection.initialOffset,
                initial_data_rows: dataRows,
                group_rect: {
                    width: groupRect.width,
                    height: groupRect.height,
                    top: groupRect.top,
                    left: groupRect.left
                },
                before_rect: {
                    width: beforeRect.width,
                    height: beforeRect.height,
                    top: beforeRect.top,
                    left: beforeRect.left
                }
            };
        }
        """
        
        return await self.page.evaluate(detection_script)
    
    async def _try_activate_virtual_scroll(self) -> bool:
        """尝试激活虚拟滚动（针对登录后可能未激活的情况）"""
        
        activation_script = """
        async () => {
            const virtualBefore = document.querySelector('.c-virtual-before');
            const virtualGroup = document.querySelector('.c-virtual-group');
            
            if (!virtualBefore || !virtualGroup) {
                return { success: false, reason: '虚拟滚动元素不存在' };
            }
            
            // 检查初始状态
            const beforeStyle = window.getComputedStyle(virtualBefore);
            const initialTransform = beforeStyle.transform;
            
            if (initialTransform !== 'none') {
                return { success: true, reason: '虚拟滚动已激活', transform: initialTransform };
            }
            
            // 尝试激活虚拟滚动的方法
            const activationMethods = [
                // 方法1: 触发wheel事件
                () => {
                    virtualGroup.dispatchEvent(new WheelEvent('wheel', {
                        deltaY: 100,
                        bubbles: true
                    }));
                },
                // 方法2: 滚动容器
                () => {
                    if (virtualGroup.scrollHeight > virtualGroup.clientHeight) {
                        virtualGroup.scrollTop = 10;
                    }
                },
                // 方法3: 滚动父容器
                () => {
                    let parent = virtualGroup.parentElement;
                    while (parent && parent !== document.body) {
                        if (parent.scrollHeight > parent.clientHeight) {
                            parent.scrollTop = 10;
                            break;
                        }
                        parent = parent.parentElement;
                    }
                },
                // 方法4: 触发window滚动
                () => {
                    window.scrollBy(0, 10);
                }
            ];
            
            // 尝试每种激活方法
            for (let i = 0; i < activationMethods.length; i++) {
                try {
                    activationMethods[i]();
                    
                    // 等待一下让DOM更新
                    await new Promise(resolve => setTimeout(resolve, 300));
                    
                    // 检查是否有变化
                    const newStyle = window.getComputedStyle(virtualBefore);
                    const newTransform = newStyle.transform;
                    
                    if (newTransform !== 'none' && newTransform !== initialTransform) {
                        return { 
                            success: true, 
                            reason: `方法${i+1}成功激活`, 
                            method: i+1,
                            transform: newTransform 
                        };
                    }
                } catch (error) {
                    // 继续尝试下一个方法
                }
            }
            
            return { success: false, reason: '所有激活方法都失败' };
        }
        """
        
        try:
            result = await self.page.evaluate(activation_script)
            
            if result['success']:
                self.logger.info(f"✅ 虚拟滚动激活成功: {result['reason']}")
                return True
            else:
                self.logger.warning(f"⚠️ 虚拟滚动激活失败: {result['reason']}")
                return False
                
        except Exception as e:
            self.logger.error(f"激活虚拟滚动时出错: {e}")
            return False
    
    async def _gentle_activate_virtual_scroll(self) -> bool:
        """轻量级激活虚拟滚动，避免重置位置"""
        
        activation_script = """
        async () => {
            const virtualBefore = document.querySelector('.c-virtual-before');
            const virtualGroup = document.querySelector('.c-virtual-group');
            
            if (!virtualBefore || !virtualGroup) {
                return { success: false, reason: '虚拟滚动元素不存在' };
            }
            
            // 轻量级激活策略：只使用wheel事件，不修改scrollTop
            try {
                // 获取当前偏移量作为基准
                const beforeStyle = window.getComputedStyle(virtualBefore);
                const currentTransform = beforeStyle.transform;
                
                // 触发轻量级wheel事件
                virtualGroup.dispatchEvent(new WheelEvent('wheel', {
                    deltaY: 50,  // 使用较小的滚动量
                    bubbles: true,
                    cancelable: true
                }));
                
                // 短暂等待
                await new Promise(resolve => setTimeout(resolve, 200));
                
                // 检查是否有响应（但不要求位置必须改变）
                const newStyle = window.getComputedStyle(virtualBefore);
                const newTransform = newStyle.transform;
                
                return { 
                    success: true, 
                    reason: '轻量级激活完成',
                    original_transform: currentTransform,
                    new_transform: newTransform
                };
                
            } catch (error) {
                return { success: false, reason: `激活失败: ${error.message}` };
            }
        }
        """
        
        try:
            result = await self.page.evaluate(activation_script)
            
            if result['success']:
                self.logger.info(f"✅ 轻量级虚拟滚动激活: {result['reason']}")
                return True
            else:
                self.logger.warning(f"⚠️ 轻量级虚拟滚动激活失败: {result['reason']}")
                return False
                
        except Exception as e:
            self.logger.error(f"轻量级激活虚拟滚动时出错: {e}")
            return False
    
    async def _execute_optimized_virtual_scroll(self, scroll_info: dict, progress_callback: Optional[Callable] = None) -> Tuple[bool, str, int]:
        """执行优化的虚拟滚动"""
        
        # 首先尝试激活虚拟滚动（针对登录后未激活的情况）
        activation_success = await self._try_activate_virtual_scroll()
        if not activation_success:
            self.logger.warning("虚拟滚动激活失败，但继续尝试滚动")
        
        scroll_count = 0
        max_scrolls = 200  # 增加最大滚动次数以确保获取全部数据
        scroll_distance = 800  # 适中的滚动距离
        stable_threshold = 8  # 增加稳定检查次数
        stable_count = 0
        no_change_count = 0  # 连续无变化计数
        
        previous_offset = scroll_info['initial_offset']
        previous_data_rows = scroll_info['initial_data_rows']
        last_meaningful_change = 0  # 记录最后一次有意义变化的滚动次数
        max_offset_seen = previous_offset  # 记录见过的最大偏移量
        offset_decrease_count = 0  # 偏移量递减计数
        
        # 新增：数据收集相关变量
        collected_data_set = set()  # 用于去重的股票代码集合
        all_collected_data = []  # 存储所有收集到的股票数据
        
        self.logger.info(f"开始优化虚拟滚动: 初始偏移={previous_offset}, 初始行数={previous_data_rows}")
        
        # 首先收集初始可见数据
        try:
            initial_data = await self._collect_current_visible_data_with_retry()
            for stock_data in initial_data:
                stock_code = stock_data.get('股票代码', '')
                if stock_code and stock_code not in collected_data_set:
                    collected_data_set.add(stock_code)
                    all_collected_data.append(stock_data)
            self.logger.info(f"收集初始数据: {len(initial_data)}条")
        except Exception as e:
            self.logger.warning(f"收集初始数据失败: {e}")
        
        while scroll_count < max_scrolls:
            try:
                scroll_count += 1
                
                # 执行滚动
                scroll_result = await self._perform_single_virtual_scroll(scroll_distance)
                
                if not scroll_result['success']:
                    self.logger.warning(f"第{scroll_count}次滚动失败: {scroll_result.get('error', 'Unknown error')}")
                    continue
                
                current_offset = scroll_result['current_offset']
                current_data_rows = scroll_result['current_data_rows']
                
                # 更新最大偏移量记录
                if current_offset > max_offset_seen:
                    max_offset_seen = current_offset
                
                # 检测偏移量异常递减（可能是重置到顶部）
                offset_significant_decrease = False
                if previous_offset > 0 and current_offset < previous_offset * 0.3:  # 偏移量减少超过70%
                    offset_decrease_count += 1
                    offset_significant_decrease = True
                    self.logger.warning(f"检测到偏移量异常递减: {previous_offset:.0f} -> {current_offset:.0f} (第{offset_decrease_count}次)")
                else:
                    offset_decrease_count = 0  # 重置递减计数
                
                # 检测是否接近底部（基于最大偏移量的80%）
                is_near_bottom = max_offset_seen > 1000 and current_offset >= max_offset_seen * 0.8
                
                # 增强的变化检测逻辑
                offset_changed = abs(current_offset - previous_offset) > 20  # 降低阈值以捕获更多变化
                data_rows_increased = current_data_rows > previous_data_rows
                meaningful_change = offset_changed or data_rows_increased
                
                # 增强的进度回调
                if progress_callback:
                    if meaningful_change:
                        progress_message = f"✅ 虚拟滚动第{scroll_count}次有效, 偏移={current_offset:.0f}px, 数据行={current_data_rows}, 最大偏移={max_offset_seen:.0f}"
                    else:
                        near_bottom_text = " [接近底部]" if is_near_bottom else ""
                        progress_message = f"⏳ 虚拟滚动第{scroll_count}次, 连续{no_change_count}次无变化, 数据行={current_data_rows}{near_bottom_text}"
                    progress_callback(scroll_count, current_data_rows, progress_message)
                
                self.logger.debug(f"第{scroll_count}次滚动: 偏移变化={offset_changed}({previous_offset:.0f}->{current_offset:.0f}), 数据行变化={data_rows_increased}({previous_data_rows}->{current_data_rows}), 最大偏移={max_offset_seen:.0f}, 接近底部={is_near_bottom}")
                
                # 检查停止条件
                should_stop = False
                stop_reason = ""
                
                # 条件1: 偏移量连续递减（可能是重置）
                if offset_decrease_count >= 2:
                    should_stop = True
                    stop_reason = f"检测到连续{offset_decrease_count}次偏移量异常递减，可能发生重置，停止滚动"
                
                # 条件2: 接近底部且连续无变化
                elif is_near_bottom and no_change_count >= 2:
                    should_stop = True
                    stop_reason = f"已接近底部(偏移={current_offset:.0f}/{max_offset_seen:.0f})且连续{no_change_count}次无变化，停止滚动"
                
                # 条件3: 连续多次无变化
                elif no_change_count >= 5:
                    should_stop = True
                    stop_reason = f"连续{no_change_count}次无变化，停止滚动"
                
                if should_stop:
                    self.logger.info(stop_reason)
                    break
                
                if meaningful_change:
                    # 有意义的变化，重置计数器
                    stable_count = 0
                    no_change_count = 0
                    last_meaningful_change = scroll_count
                    previous_offset = current_offset
                    previous_data_rows = current_data_rows
                    
                    # 新增：收集当前可见的股票数据（使用重试机制）
                    try:
                        current_data = await self._collect_current_visible_data_with_retry()
                        new_data_count = 0
                        for stock_data in current_data:
                            stock_code = stock_data.get('股票代码', '')
                            if stock_code and stock_code not in collected_data_set:
                                collected_data_set.add(stock_code)
                                all_collected_data.append(stock_data)
                                new_data_count += 1
                        
                        if new_data_count > 0:
                            self.logger.info(f"✅ 第{scroll_count}次滚动有效: 偏移={current_offset:.0f}, 数据行={current_data_rows}, 新收集{new_data_count}只股票, 总计{len(all_collected_data)}只")
                        else:
                            self.logger.info(f"✅ 第{scroll_count}次滚动有效: 偏移={current_offset:.0f}, 数据行={current_data_rows}, 无新数据, 总计{len(all_collected_data)}只")
                            
                    except Exception as e:
                        self.logger.warning(f"第{scroll_count}次滚动后收集数据失败: {e}")
                        self.logger.info(f"✅ 第{scroll_count}次滚动有效: 偏移={current_offset:.0f}, 数据行={current_data_rows}")
                else:
                    # 无变化，增加稳定计数
                    stable_count += 1
                    no_change_count += 1
                    
                    # 如果连续很多次无变化且不在特殊条件下，尝试一次激活（但要避免重置位置）
                    if no_change_count == 3 and not is_near_bottom and max_offset_seen < 2000:
                        self.logger.info("连续3次无变化且不在底部，尝试轻量级激活虚拟滚动...")
                        await self._gentle_activate_virtual_scroll()
                    
                    if stable_count >= stable_threshold:
                        # 检查是否真的完成了
                        if last_meaningful_change > 0 and (scroll_count - last_meaningful_change) >= stable_threshold:
                            self.logger.info(f"连续{stable_count}次无变化且最后有效滚动在第{last_meaningful_change}次，认为滚动完成")
                            break
                        elif last_meaningful_change == 0:
                            self.logger.warning(f"从未检测到有效滚动，可能虚拟滚动未正确工作")
                            break
                
                # 动态等待时间：有变化时等待更久以确保数据加载，使用配置的等待时间
                wait_time = self.data_change_wait if meaningful_change else self.scroll_wait_time * 0.5
                await asyncio.sleep(wait_time)
                
            except Exception as e:
                self.logger.warning(f"第{scroll_count}次滚动异常: {e}")
                # 异常计数，如果异常太多就停止
                if hasattr(self, '_exception_count'):
                    self._exception_count += 1
                else:
                    self._exception_count = 1
                
                if self._exception_count >= 10:
                    self.logger.error("滚动异常次数过多，停止滚动")
                    break
                continue
        
        # 收集最后一批数据（使用重试机制）
        try:
            final_data = await self._collect_current_visible_data_with_retry()
            for stock_data in final_data:
                stock_code = stock_data.get('股票代码', '')
                if stock_code and stock_code not in collected_data_set:
                    collected_data_set.add(stock_code)
                    all_collected_data.append(stock_data)
            self.logger.info(f"收集最终数据: 新增{len(final_data)}条, 总计{len(all_collected_data)}条")
        except Exception as e:
            self.logger.warning(f"收集最终数据失败: {e}")
        
        # 获取最终数据
        final_data_rows = await self._get_final_data_count()
        success_message = f"虚拟滚动完成: 共滚动{scroll_count}次, DOM数据行数={final_data_rows}, 实际收集{len(all_collected_data)}只股票"
        
        # 将收集到的数据存储到实例变量中，供后续使用
        self.collected_stock_data = all_collected_data
        
        # 执行数据完整性检查
        integrity_result = await self.check_data_integrity()
        if not integrity_result['is_complete']:
            self.logger.warning(f"数据完整性检查发现问题，但继续执行")
        
        if progress_callback:
            progress_callback(scroll_count, len(all_collected_data), f"虚拟滚动加载完成，共收集{len(all_collected_data)}只股票数据")
        
        self.logger.info(success_message)
        return True, success_message, len(all_collected_data)
    
    async def _perform_single_virtual_scroll(self, distance: int) -> dict:
        """执行单次虚拟滚动"""
        
        scroll_script = f"""
        async (distance) => {{
            try {{
                // 查找虚拟滚动容器
                const virtualGroup = document.querySelector('.c-virtual-group');
                const virtualBefore = document.querySelector('.c-virtual-before');
                
                if (!virtualGroup || !virtualBefore) {{
                    return {{ success: false, error: '虚拟滚动元素不存在' }};
                }}
                
                // 查找实际的滚动容器
                let scrollContainer = virtualGroup;
                let parent = virtualGroup.parentElement;
                while (parent && parent !== document.body) {{
                    const style = window.getComputedStyle(parent);
                    if (style.overflow === 'auto' || style.overflow === 'scroll' || 
                        style.overflowY === 'auto' || style.overflowY === 'scroll') {{
                        scrollContainer = parent;
                        break;
                    }}
                    parent = parent.parentElement;
                }}
                
                // 执行滚动 - 多种策略并行尝试
                let scrollExecuted = false;
                
                // 策略1: 滚动检测到的容器
                if (scrollContainer === virtualGroup && virtualGroup.scrollHeight > virtualGroup.clientHeight) {{
                    virtualGroup.scrollTop += distance;
                    scrollExecuted = true;
                }} else if (scrollContainer !== virtualGroup) {{
                    scrollContainer.scrollTop += distance;
                    scrollExecuted = true;
                }}
                
                // 策略2: 同时触发wheel事件以激活虚拟滚动
                virtualGroup.dispatchEvent(new WheelEvent('wheel', {{
                    deltaY: distance,
                    bubbles: true,
                    cancelable: true
                }}));
                
                // 策略3: 如果上述方法无效，使用window滚动
                if (!scrollExecuted) {{
                    window.scrollBy(0, distance);
                }}
                
                // 等待DOM更新
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 获取更新后的状态
                const beforeStyle = window.getComputedStyle(virtualBefore);
                const transform = beforeStyle.transform;
                let currentOffset = 0;
                
                if (transform && transform !== 'none') {{
                    const translateYMatch = transform.match(/translateY\\(([^)]+)\\)/);
                    const matrixMatch = transform.match(/matrix\\([^,]+,[^,]+,[^,]+,[^,]+,[^,]+,([^)]+)\\)/);
                    
                    if (translateYMatch) {{
                        currentOffset = Math.abs(parseFloat(translateYMatch[1].replace('px', '')));
                    }} else if (matrixMatch) {{
                        currentOffset = Math.abs(parseFloat(matrixMatch[1]));
                    }}
                }} else {{
                    // 如果没有transform，尝试其他方式获取偏移
                    currentOffset = virtualBefore.offsetHeight || 0;
                }}
                
                // 使用多种选择器策略统计数据行
                const selectors = ['.c-table-row-body', '.c-virtual-group .c-table-row', '[class*="row-body"]', '[class*="table-row"]', 'tr:not(.c-table-header)'];
                let currentDataRows = 0;
                for (const selector of selectors) {{
                    try {{
                        const count = document.querySelectorAll(selector).length;
                        if (count > currentDataRows) currentDataRows = count;
                    }} catch (e) {{}}
                }}
                
                return {{
                    success: true,
                    current_offset: currentOffset,
                    current_data_rows: currentDataRows,
                    scroll_container: scrollContainer.className || scrollContainer.tagName,
                    transform: transform
                }};
                
            }} catch (error) {{
                return {{ success: false, error: error.message }};
            }}
        }}
        """
        
        return await self.page.evaluate(scroll_script, distance)
    
    async def _get_final_data_count(self) -> int:
        """获取最终数据行数"""
        
        count_script = """
        () => {
            // 多种选择器策略，确保能找到所有数据行
            const selectors = [
                '.c-table-row-body',
                '.c-virtual-group .c-table-row',
                '[class*="row-body"]',
                '[class*="table-row"]',
                'tr:not(.c-table-header)',
                '.c-virtual-group > div > div'  // 通用的虚拟滚动行选择器
            ];
            
            let maxCount = 0;
            let bestSelector = '';
            
            for (const selector of selectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > maxCount) {
                        maxCount = elements.length;
                        bestSelector = selector;
                    }
                } catch (e) {
                    // 忽略无效选择器
                }
            }
            
            console.log(`最终数据统计: 使用选择器 "${bestSelector}" 找到 ${maxCount} 行数据`);
            return maxCount;
        }
        """
        
        try:
            return await self.page.evaluate(count_script)
        except:
            return 0
    
    async def _fallback_to_normal_scroll(self, progress_callback: Optional[Callable] = None) -> Tuple[bool, str, int]:
        """回退到普通滚动模式"""
        
        self.logger.info("回退到普通滚动模式...")
        
        scroll_count = 0
        max_scrolls = 50
        scroll_distance = 800
        stable_count = 0
        stable_threshold = 5
        
        previous_scroll_y = 0
        previous_data_rows = 0
        
        while scroll_count < max_scrolls:
            try:
                scroll_count += 1
                
                # 执行普通滚动
                scroll_result = await self.page.evaluate(f"""
                () => {{
                    window.scrollBy(0, {scroll_distance});
                    
                    // 等待一下
                    return new Promise(resolve => {{
                        setTimeout(() => {{
                            resolve({{
                                scrollY: window.pageYOffset || document.documentElement.scrollTop,
                                scrollHeight: document.documentElement.scrollHeight,
                                clientHeight: window.innerHeight,
                                dataRows: document.querySelectorAll('.c-table-row-body, [class*="row"], tr').length
                            }});
                        }}, 1000);
                    }});
                }}
                """)
                
                current_scroll_y = scroll_result['scrollY']
                current_data_rows = scroll_result['dataRows']
                
                # 进度回调
                if progress_callback:
                    message = f"普通滚动第{scroll_count}次, 位置={current_scroll_y:.0f}px, 数据行={current_data_rows}"
                    progress_callback(scroll_count, current_data_rows, message)
                
                # 检查是否到达底部
                bottom_distance = scroll_result['scrollHeight'] - (current_scroll_y + scroll_result['clientHeight'])
                at_bottom = bottom_distance < 100
                
                # 检查滚动是否有效果
                scroll_changed = abs(current_scroll_y - previous_scroll_y) > 10
                data_changed = current_data_rows > previous_data_rows
                
                if scroll_changed or data_changed:
                    stable_count = 0
                    previous_scroll_y = current_scroll_y
                    previous_data_rows = current_data_rows
                else:
                    stable_count += 1
                
                if at_bottom and stable_count >= stable_threshold:
                    self.logger.info(f"到达页面底部且滚动稳定，停止滚动")
                    break
                
                await asyncio.sleep(self.scroll_wait_time * 0.75)  # 使用配置的等待时间
                
            except Exception as e:
                self.logger.warning(f"普通滚动第{scroll_count}次异常: {e}")
                continue
        
        final_message = f"普通滚动完成: 共滚动{scroll_count}次, 最终数据行数={current_data_rows}"
        
        if progress_callback:
            progress_callback(scroll_count, current_data_rows, "普通滚动加载完成")
        
        self.logger.info(final_message)
        return True, final_message, current_data_rows

    async def _collect_current_visible_data_with_retry(self, max_retries=2):
        """
        带重试机制的数据收集方法
        
        Args:
            max_retries: 最大重试次数
            
        Returns:
            收集到的股票数据列表
        """
        for attempt in range(max_retries + 1):
            try:
                collected_data = await self._collect_current_visible_data()
                
                # 如果收集到的数据为空或过少，进行重试
                if len(collected_data) == 0 and attempt < max_retries:
                    self.logger.warning(f"数据收集结果为空，第{attempt + 1}次重试...")
                    await asyncio.sleep(1.2)  # 额外等待后重试
                    continue
                    
                return collected_data
                
            except Exception as e:
                if attempt < max_retries:
                    self.logger.warning(f"数据收集失败（第{attempt + 1}次尝试），错误: {e}，将重试...")
                    await asyncio.sleep(1.0)
                else:
                    self.logger.error(f"数据收集最终失败，已重试{max_retries}次: {e}")
                    return []
        
        return []

    async def _collect_current_visible_data(self):
        """收集当前可见的股票数据，确保DOM完全渲染"""
        try:
            # 额外等待以确保虚拟滚动的DOM完全渲染
            await asyncio.sleep(0.8)
            
            # 获取当前页面的HTML内容
            html_content = await self.page.content()
            
            # 使用HTML解析器提取股票数据
            from html_data_parser import create_html_data_parser_manager
            html_parser_manager = create_html_data_parser_manager()
            
            # 解析当前可见的股票数据
            stock_data_list = html_parser_manager.parse_stock_data_from_html(html_content)
            
            self.logger.debug(f"HTML解析器返回 {len(stock_data_list)} 条原始数据")
            
            # 转换数据格式
            collected_data = []
            invalid_data_count = 0
            
            for index, stock in enumerate(stock_data_list):
                try:
                    stock_data = {
                        '股票代码': stock.get('stock_code', ''),
                        '股票名称': stock.get('stock_name', ''),
                        '小草竞王': stock.get('jingwang', ''),
                        '小草红盘起爆': stock.get('hongpan_qibao', ''),
                        '小草绿盘低吸': stock.get('lvpan_dixi', ''),
                        '小草连板接力': stock.get('lianban_jieli', '')
                    }
                    
                    # 验证必要字段
                    stock_code = stock_data['股票代码'].strip()
                    stock_name = stock_data['股票名称'].strip()
                    
                    if stock_code and stock_name:
                        collected_data.append(stock_data)
                    else:
                        invalid_data_count += 1
                        self.logger.debug(f"第{index+1}条数据无效: 代码='{stock_code}', 名称='{stock_name}'")
                        
                except Exception as e:
                    invalid_data_count += 1
                    self.logger.warning(f"处理第{index+1}条股票数据时出错: {e}")
                    continue
            
            if invalid_data_count > 0:
                self.logger.debug(f"本次收集：有效数据 {len(collected_data)} 条，无效数据 {invalid_data_count} 条")
            
            return collected_data
            
        except Exception as e:
            self.logger.error(f"收集当前可见数据失败: {e}")
            return []

    def get_collected_data(self):
        """获取滚动过程中收集到的所有股票数据"""
        return getattr(self, 'collected_stock_data', [])
    
    async def check_data_integrity(self, expected_total=None):
        """
        检查数据完整性，确保收集到的数据数量合理
        
        Args:
            expected_total: 预期的总数据量（如果知道的话）
            
        Returns:
            dict: 包含完整性检查结果的字典
        """
        try:
            collected_data = self.get_collected_data()
            collected_count = len(collected_data)
            
            # 获取当前DOM中的数据行数
            dom_rows_script = """
            () => {
                const dataRows = document.querySelectorAll('.c-table-row-body');
                return dataRows.length;
            }
            """
            
            dom_row_count = await self.page.evaluate(dom_rows_script)
            
            # 分析数据完整性
            integrity_result = {
                'collected_count': collected_count,
                'dom_row_count': dom_row_count,
                'is_complete': True,
                'missing_count': 0,
                'completion_rate': 0.0,
                'issues': []
            }
            
            if expected_total:
                integrity_result['expected_total'] = expected_total
                integrity_result['missing_count'] = max(0, expected_total - collected_count)
                integrity_result['completion_rate'] = (collected_count / expected_total) * 100 if expected_total > 0 else 0
                
                if collected_count < expected_total * 0.95:  # 缺失超过5%认为不完整
                    integrity_result['is_complete'] = False
                    integrity_result['issues'].append(f"数据收集不完整：期望{expected_total}，实际{collected_count}")
            
            # 检查数据质量
            empty_code_count = sum(1 for data in collected_data if not data.get('股票代码', '').strip())
            empty_name_count = sum(1 for data in collected_data if not data.get('股票名称', '').strip())
            
            if empty_code_count > 0:
                integrity_result['issues'].append(f"发现{empty_code_count}条股票代码为空的记录")
            if empty_name_count > 0:
                integrity_result['issues'].append(f"发现{empty_name_count}条股票名称为空的记录")
            
            # 记录完整性检查结果
            if integrity_result['is_complete']:
                self.logger.info(f"✅ 数据完整性检查通过: 收集{collected_count}条数据，DOM中{dom_row_count}行")
            else:
                self.logger.warning(f"⚠️ 数据完整性检查发现问题: {'; '.join(integrity_result['issues'])}")
            
            return integrity_result
            
        except Exception as e:
            self.logger.error(f"数据完整性检查失败: {e}")
            return {
                'collected_count': len(self.get_collected_data()),
                'is_complete': False,
                'issues': [f"检查过程出错: {str(e)}"]
            }


# 集成到现有的WebAutomator类中
async def optimized_scroll_and_load_all_data(web_automator_instance, progress_callback=None):
    """
    替换WebAutomator中的_scroll_and_load_all_data方法的优化版本
    可以直接替换原有方法或作为新方法使用
    """
    handler = OptimizedVirtualScrollHandler(web_automator_instance.page, web_automator_instance.logger)
    return await handler.scroll_and_load_all_data(progress_callback)