# -*- coding: utf-8 -*-
"""
版本管理工具模块
从 CHANGELOG.md 文档中读取版本信息
"""

import os
import re
import logging
from typing import Optional


def get_current_version() -> str:
    """
    从 CHANGELOG.md 文件中读取当前版本号
    
    Returns:
        str: 当前版本号，格式如 'v2025.07.28.1517'，读取失败时返回默认版本号
    """
    logger = logging.getLogger(__name__)
    default_version = "v1.0.0.0000"
    
    try:
        # 获取 CHANGELOG.md 文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        changelog_path = os.path.join(current_dir, "CHANGELOG.md")
        
        if not os.path.exists(changelog_path):
            logger.warning(f"CHANGELOG.md 文件不存在: {changelog_path}")
            return default_version
        
        with open(changelog_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 首先尝试从顶部的"当前版本"行读取
        current_version_pattern = r'当前版本：\*\*(v[\d.]+)\*\*'
        match = re.search(current_version_pattern, content)
        
        if match:
            version = match.group(1)
            logger.debug(f"从当前版本标识读取到版本号: {version}")
            return version
        
        # 如果没有找到，尝试从第一个版本标题读取
        version_header_pattern = r'## \[(v[\d.]+)\]'
        match = re.search(version_header_pattern, content)
        
        if match:
            version = match.group(1)
            logger.debug(f"从版本标题读取到版本号: {version}")
            return version
        
        logger.warning("未能从 CHANGELOG.md 中解析到版本号")
        return default_version
        
    except Exception as e:
        logger.error(f"读取版本号时发生错误: {e}")
        return default_version


def get_version_info() -> dict:
    """
    获取详细的版本信息
    
    Returns:
        dict: 包含版本号、日期等信息的字典
    """
    logger = logging.getLogger(__name__)
    
    version_info = {
        'version': get_current_version(),
        'date': None,
        'description': None
    }
    
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        changelog_path = os.path.join(current_dir, "CHANGELOG.md")
        
        if not os.path.exists(changelog_path):
            return version_info
        
        with open(changelog_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取最新版本的日期
        version_pattern = rf'## \[{re.escape(version_info["version"])}\] - ([\d-]+)'
        match = re.search(version_pattern, content)
        
        if match:
            version_info['date'] = match.group(1)
            logger.debug(f"提取到版本日期: {version_info['date']}")
        
        return version_info
        
    except Exception as e:
        logger.error(f"获取版本详细信息时发生错误: {e}")
        return version_info


if __name__ == "__main__":
    # 测试代码
    print(f"当前版本: {get_current_version()}")
    print(f"版本信息: {get_version_info()}")