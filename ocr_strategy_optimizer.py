# -*- coding: utf-8 -*-
"""
OCR策略优化模块
提供早期退出和缓存优化的OCR策略执行器
"""

import time
import logging
import cv2
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass


@dataclass
class OCRResult:
    """OCR识别结果"""
    text: str
    confidence: float
    engine: str
    strategy: str
    position: Tuple[int, int, int, int]
    success: bool = True
    error: str = ""


@dataclass
class StrategyResult:
    """策略执行结果"""
    strategy_name: str
    ocr_results: List[OCRResult]
    total_confidence: float
    best_result: Optional[OCRResult]
    success: bool
    execution_time: float


class OptimizedOCRStrategy:
    """优化的OCR策略执行器"""
    
    def __init__(self, fund_ocr_instance):
        """
        初始化策略执行器
        
        Args:
            fund_ocr_instance: FundDataOCR实例
        """
        self.logger = logging.getLogger(__name__)
        self.fund_ocr = fund_ocr_instance
        
        # 性能配置
        self.early_exit_threshold = 0.8  # 置信度阈值，超过此值即可早期退出
        self.min_results_threshold = 3   # 最少结果数量阈值
        self.enable_caching = True       # 是否启用图像预处理缓存
        
        # 缓存
        self.preprocessing_cache = {}    # 预处理结果缓存
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 统计信息
        self.strategy_stats = {}  # 各策略的成功率统计
        self.early_exits = 0      # 早期退出次数
        self.total_executions = 0 # 总执行次数
    
    def execute_strategies_with_early_exit(self, image: np.ndarray, 
                                         strategies: List[Tuple[str, Any]], 
                                         recognition_func: callable) -> Dict[str, Any]:
        """
        执行多策略OCR识别，支持早期退出
        
        Args:
            image: 输入图像
            strategies: 策略列表 [(策略名, 预处理参数), ...]
            recognition_func: 识别函数
            
        Returns:
            最佳识别结果
        """
        self.total_executions += 1
        start_time = time.time()
        
        all_results = []
        best_result = None
        best_confidence = 0.0
        accumulated_data = {}
        
        try:
            for i, (strategy_name, preprocessing_params) in enumerate(strategies):
                strategy_start = time.time()
                
                # 获取预处理图像（使用缓存）
                processed_image = self._get_processed_image(image, strategy_name, preprocessing_params)
                
                if processed_image is None:
                    continue
                
                # 执行OCR识别
                strategy_result = recognition_func(processed_image, strategy_name)
                strategy_time = time.time() - strategy_start
                
                if strategy_result and strategy_result.get('success', False):
                    # 更新统计信息
                    self._update_strategy_stats(strategy_name, True, strategy_time)
                    
                    # 提取有用数据
                    extracted_data = self._extract_useful_data(strategy_result)
                    if extracted_data:
                        # 合并到累积数据中
                        for key, value in extracted_data.items():
                            if key not in accumulated_data:
                                accumulated_data[key] = value
                    
                    # 检查是否满足早期退出条件
                    current_confidence = strategy_result.get('best_confidence', 0.0)
                    if current_confidence > best_confidence:
                        best_confidence = current_confidence
                        best_result = strategy_result
                    
                    # 早期退出检查
                    if self._should_early_exit(accumulated_data, best_confidence, i, len(strategies)):
                        self.early_exits += 1
                        self.logger.debug(f"早期退出：策略{strategy_name}(第{i+1}/{len(strategies)}个)，"
                                        f"置信度{best_confidence:.3f}，数据量{len(accumulated_data)}")
                        break
                else:
                    # 更新失败统计
                    self._update_strategy_stats(strategy_name, False, strategy_time)
                
                all_results.append({
                    'strategy': strategy_name,
                    'result': strategy_result,
                    'time': strategy_time
                })
            
            # 构建最终结果
            total_time = time.time() - start_time
            final_result = self._build_final_result(accumulated_data, best_result, all_results, total_time)
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"策略执行出错: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _get_processed_image(self, image: np.ndarray, strategy_name: str, preprocessing_params: Any) -> Optional[np.ndarray]:
        """
        获取预处理图像（支持缓存）
        
        Args:
            image: 原始图像
            strategy_name: 策略名称
            preprocessing_params: 预处理参数
            
        Returns:
            预处理后的图像
        """
        try:
            # 生成缓存键
            if self.enable_caching:
                cache_key = self._generate_cache_key(image, strategy_name, preprocessing_params)
                
                # 检查缓存
                if cache_key in self.preprocessing_cache:
                    self.cache_hits += 1
                    return self.preprocessing_cache[cache_key]
                
                self.cache_misses += 1
            
            # 执行预处理
            if hasattr(preprocessing_params, '__call__'):
                # 如果是函数，直接调用
                processed_image = preprocessing_params(image)
            elif isinstance(preprocessing_params, np.ndarray):
                # 如果已经是预处理后的图像
                processed_image = preprocessing_params
            else:
                # 根据策略名选择预处理方法
                processed_image = self._apply_preprocessing_by_name(image, strategy_name)
            
            # 缓存结果
            if self.enable_caching and processed_image is not None:
                # 限制缓存大小，避免内存过度使用
                if len(self.preprocessing_cache) < 50:
                    self.preprocessing_cache[cache_key] = processed_image.copy()
            
            return processed_image
            
        except Exception as e:
            self.logger.error(f"预处理图像失败 ({strategy_name}): {str(e)}")
            return None
    
    def _generate_cache_key(self, image: np.ndarray, strategy_name: str, preprocessing_params: Any) -> str:
        """生成缓存键"""
        # 使用图像哈希和策略名作为缓存键
        image_hash = hash(image.tobytes())
        params_hash = hash(str(preprocessing_params))
        return f"{strategy_name}_{image_hash}_{params_hash}"
    
    def _apply_preprocessing_by_name(self, image: np.ndarray, strategy_name: str) -> Optional[np.ndarray]:
        """根据策略名应用预处理"""
        try:
            if "无预处理" in strategy_name:
                return image
            elif "百分比专用v2" in strategy_name or "保守" in strategy_name:
                return self.fund_ocr._preprocess_for_percentage_v2(image)
            elif "百分比专用v1" in strategy_name or "标准" in strategy_name:
                return self.fund_ocr._preprocess_for_percentage(image)
            elif "放大2倍" in strategy_name:
                return self.fund_ocr._preprocess_scale_only(image, 2.0)
            elif "放大3倍" in strategy_name:
                return self.fund_ocr._preprocess_scale_only(image, 3.0)
            elif "简化预处理" in strategy_name:
                return self.fund_ocr._preprocess_image_simple(image)
            elif "灰度化" in strategy_name:
                return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            elif "CPU优化" in strategy_name:
                return self.fund_ocr._preprocess_image_cpu_optimized(image)
            else:
                self.logger.warning(f"未知预处理策略: {strategy_name}")
                return image
        except Exception as e:
            self.logger.error(f"应用预处理失败 ({strategy_name}): {str(e)}")
            return None
    
    def _extract_useful_data(self, strategy_result: Dict[str, Any]) -> Dict[str, Any]:
        """从策略结果中提取有用数据"""
        useful_data = {}
        
        # 提取fund_values
        if 'fund_values' in strategy_result:
            fund_values = strategy_result['fund_values']
            if fund_values:
                for i, value in enumerate(fund_values):
                    key = f'fund_{i}'
                    if key not in useful_data:  # 避免重复
                        useful_data[key] = value
        
        # 提取其他有用数据
        for key in ['today_fund', 'yesterday_fund', 'day_before_yesterday_fund']:
            if key in strategy_result and key not in useful_data:
                useful_data[key] = strategy_result[key]
        
        return useful_data
    
    def _should_early_exit(self, accumulated_data: Dict[str, Any], 
                          best_confidence: float, 
                          current_index: int, 
                          total_strategies: int) -> bool:
        """判断是否应该早期退出"""
        # 条件1：置信度足够高
        if best_confidence >= self.early_exit_threshold:
            return True
        
        # 条件2：已获得足够的数据点
        if len(accumulated_data) >= self.min_results_threshold:
            return True
        
        # 条件3：已经尝试了大部分策略但效果不佳
        if current_index >= total_strategies * 0.7 and best_confidence < 0.3:
            return False  # 继续尝试，可能需要后面的策略
        
        return False
    
    def _build_final_result(self, accumulated_data: Dict[str, Any], 
                           best_result: Optional[Dict[str, Any]], 
                           all_results: List[Dict[str, Any]], 
                           total_time: float) -> Dict[str, Any]:
        """构建最终结果"""
        return {
            'success': len(accumulated_data) > 0,
            'accumulated_data': accumulated_data,
            'best_result': best_result,
            'all_strategy_results': all_results,
            'execution_time': total_time,
            'strategies_tried': len(all_results),
            'early_exit': len(all_results) < len(self.strategy_stats) if self.strategy_stats else False,
            'cache_stats': {
                'hits': self.cache_hits,
                'misses': self.cache_misses,
                'hit_rate': self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0
            }
        }
    
    def _update_strategy_stats(self, strategy_name: str, success: bool, execution_time: float):
        """更新策略统计信息"""
        if strategy_name not in self.strategy_stats:
            self.strategy_stats[strategy_name] = {
                'attempts': 0,
                'successes': 0,
                'total_time': 0.0,
                'avg_time': 0.0
            }
        
        stats = self.strategy_stats[strategy_name]
        stats['attempts'] += 1
        stats['total_time'] += execution_time
        stats['avg_time'] = stats['total_time'] / stats['attempts']
        
        if success:
            stats['successes'] += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        total_attempts = sum(stats['attempts'] for stats in self.strategy_stats.values())
        total_successes = sum(stats['successes'] for stats in self.strategy_stats.values())
        
        return {
            'total_executions': self.total_executions,
            'early_exits': self.early_exits,
            'early_exit_rate': self.early_exits / self.total_executions if self.total_executions > 0 else 0,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'cache_hit_rate': self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
            'strategy_stats': self.strategy_stats,
            'overall_success_rate': total_successes / total_attempts if total_attempts > 0 else 0
        }
    
    def clear_cache(self):
        """清理缓存"""
        self.preprocessing_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        self.logger.info("OCR策略缓存已清理")