# -*- coding: utf-8 -*-
"""
网页自动化模块
基于Playwright的topxlc.com小草选股数据抓取
"""

import asyncio
import logging
import os
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, <PERSON>, <PERSON>rowser, BrowserContext

from config import WEB_AUTOMATION_CONFIG
from html_data_parser import create_html_data_parser_manager


class WebAutomator:
    """网页自动化操作类"""
    
    def __init__(self):
        """初始化网页自动化操作器"""
        self.config = WEB_AUTOMATION_CONFIG
        self.logger = logging.getLogger(__name__)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.is_logged_in = False
        self.is_ready_for_extraction = False
        
    async def initialize_browser(self):
        """初始化浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=self.config['browser_headless'],
                timeout=self.config['browser_timeout']
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {str(e)}")
            return False
    
    async def navigate_to_site(self):
        """导航到目标网站"""
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            # 导航到目标网站
            await self.page.goto(self.config['target_url'])
            
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(self.config['page_load_wait'])
            
            self.logger.info(f"成功导航到: {self.config['target_url']}")
            return True
            
        except Exception as e:
            self.logger.error(f"导航到网站失败: {str(e)}")
            return False
    
    async def auto_login(self, username: str, password: str):
        """自动登录功能"""
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            # 加载选择器配置
            from web_element_selector import get_web_element_selector_manager
            selector_manager = get_web_element_selector_manager()
            selectors = selector_manager.load_selectors_from_config()
            
            if not selectors:
                self.logger.warning("未找到选择器配置，跳过自动登录")
                return True  # 返回True让用户手动登录
            
            self.logger.info("开始自动登录...")
            
            # 等待并填写用户名
            if 'username_input' in selectors:
                username_selector = selectors['username_input']
                self.logger.info(f"查找用户名输入框: {username_selector}")
                
                await self.page.wait_for_selector(username_selector, timeout=10000)
                await self.page.fill(username_selector, username)
                self.logger.info("用户名已填写")
                
                # 等待一下避免操作过快
                await asyncio.sleep(0.5)
            
            # 等待并填写密码
            if 'password_input' in selectors:
                password_selector = selectors['password_input']
                self.logger.info(f"查找密码输入框: {password_selector}")
                
                await self.page.wait_for_selector(password_selector, timeout=10000)
                await self.page.fill(password_selector, password)
                self.logger.info("密码已填写")
                
                # 等待一下避免操作过快
                await asyncio.sleep(0.5)
            
            # 用户名和密码填写完成，提示用户手动点击登录
            self.logger.info("✅ 用户名和密码填写完成，请手动点击登录按钮")
            self.is_logged_in = True  # 标记为已处理登录流程
            return True
                
        except Exception as e:
            self.logger.error(f"自动登录失败: {str(e)}")
            # 自动登录失败不算致命错误，返回True让用户手动登录
            return True
    
    def wait_for_user_navigation(self):
        """等待用户手动导航到指定页面"""
        """
        这个函数在GUI中被调用，提示用户手动导航到数据页面
        然后用户点击"开始抓取"按钮继续
        """
        self.logger.info("等待用户手动导航到数据页面...")
        return True
    
    def set_ready_for_extraction(self):
        """设置准备开始数据抓取状态"""
        self.is_ready_for_extraction = True
        self.logger.info("已设置为准备抓取数据状态")
    
    async def _scroll_and_load_all_data(self, progress_callback=None):
        """
        滚动页面并加载所有数据（支持虚拟滚动和网络请求监听）
        
        Args:
            progress_callback: 进度回调函数，接收 (scroll_count, current_rows, message) 参数
            
        Returns:
            tuple: (success, message, final_row_count)
        """
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            # 获取滚动配置
            scroll_config = self.config.get('scroll_loading', {})
            if not scroll_config.get('enabled', True):
                self.logger.info("滚动加载功能已禁用，跳过滚动")
                return True, "滚动加载已禁用", 0
            
            strategy_config = scroll_config.get('scroll_strategy', {})
            data_config = scroll_config.get('data_integrity', {})
            progress_config = scroll_config.get('progress_feedback', {})
            virtual_config = scroll_config.get('virtual_scroll_optimization', {})
            
            # 滚动参数
            scroll_distance = strategy_config.get('scroll_distance', 600)
            scroll_wait_time = strategy_config.get('scroll_wait_time', 2.5)
            stable_check_count = strategy_config.get('stable_check_count', 8)
            max_scroll_attempts = strategy_config.get('max_scroll_attempts', 200)
            data_change_wait = strategy_config.get('data_change_wait', 1.5)
            
            # 超时配置
            loading_timeout = data_config.get('loading_timeout', 900)
            single_scroll_timeout = data_config.get('single_scroll_timeout', 20)
            
            # 进度配置
            show_progress = progress_config.get('show_progress', True)
            update_frequency = progress_config.get('update_frequency', 1)
            progress_template = progress_config.get('progress_template', 
                                                  '正在滚动加载数据... 第{scroll_count}次滚动，滚动位置{scroll_position}px')
            
            # 虚拟滚动优化配置
            virtual_scroll_enabled = virtual_config.get('enabled', True)
            position_change_threshold = virtual_config.get('position_change_threshold', 50)
            bottom_threshold = virtual_config.get('bottom_threshold', 200)
            force_bottom_attempts = virtual_config.get('force_bottom_attempts', 5)
            
            # 网络请求监听配置
            network_monitoring_enabled = self.config.get('network_monitoring', {}).get('enabled', True)
            api_patterns = self.config.get('network_monitoring', {}).get('api_patterns', [
                '**/api/**',
                '**/data/**',
                '**/*stock*',
                '**/*list*'
            ])
            
            self.logger.info(f"开始智能滚动加载所有数据（支持虚拟滚动和网络监听={network_monitoring_enabled}）...")
            
            # 步骤1: 检测滚动容器
            scroll_container_info = await self._detect_scroll_container()
            if not scroll_container_info['found']:
                self.logger.warning("未找到滚动容器，使用window滚动")
                container_script = "window"
            else:
                container_script = scroll_container_info['script']
                self.logger.info(f"找到滚动容器: {scroll_container_info['description']}")
            
            # 记录滚动统计
            scroll_count = 0
            stable_count = 0
            start_time = asyncio.get_event_loop().time()
            previous_scroll_position = 0
            
            # 获取初始滚动信息（增强虚拟滚动检测）
            try:
                initial_scroll_script = f"""
                () => {{
                    const container = {container_script};
                    
                    // 增强虚拟滚动检测 - 多重选择器策略
                    function detectVirtualScroll() {{
                        // 虚拟滚动选择器候选列表（按优先级排序）
                        const virtualSelectors = [
                            {{ group: '.c-virtual-group', before: '.c-virtual-before' }},
                            {{ group: '.virtual-group', before: '.virtual-before' }},
                            {{ group: '[class*="virtual"]', before: '[class*="virtual"][class*="before"]' }},
                            {{ group: '.ant-virtual-list', before: '.ant-virtual-list-holder-inner' }},
                            {{ group: '.el-virtual-list', before: '.el-virtual-list-item' }},
                            {{ group: '[data-virtual="true"]', before: '[data-virtual-before]' }}
                        ];
                        
                        for (const selector of virtualSelectors) {{
                            const groupEl = document.querySelector(selector.group);
                            const beforeEl = document.querySelector(selector.before);
                            
                            if (groupEl && beforeEl) {{
                                console.log(`检测到虚拟滚动: ${{selector.group}} + ${{selector.before}}`);
                                return {{ group: groupEl, before: beforeEl, selector: selector }};
                            }}
                        }}
                        
                        // 智能检测：查找可能的虚拟滚动容器
                        const allElements = document.querySelectorAll('*');
                        for (const el of allElements) {{
                            const classList = Array.from(el.classList);
                            const hasVirtualClass = classList.some(cls => 
                                cls.includes('virtual') || 
                                cls.includes('scroll') || 
                                cls.includes('list-container')
                            );
                            
                            if (hasVirtualClass && el.children.length > 0) {{
                                // 查找可能的before元素
                                const beforeEl = Array.from(el.children).find(child => {{
                                    const childClasses = Array.from(child.classList);
                                    return childClasses.some(cls => 
                                        cls.includes('before') || 
                                        cls.includes('offset') ||
                                        child.style.transform
                                    );
                                }});
                                
                                if (beforeEl) {{
                                    console.log(`智能检测到虚拟滚动: ${{el.className}} + ${{beforeEl.className}}`);
                                    return {{ 
                                        group: el, 
                                        before: beforeEl, 
                                        selector: {{ group: '.' + Array.from(el.classList).join('.'), before: '.' + Array.from(beforeEl.classList).join('.') }}
                                    }};
                                }}
                            }}
                        }}
                        
                        return null;
                    }}
                    
                    // 基础滚动信息
                    let scrollInfo;
                    if (container === window) {{
                        scrollInfo = {{
                            scrollTop: window.pageYOffset || document.documentElement.scrollTop,
                            scrollHeight: document.documentElement.scrollHeight,
                            clientHeight: window.innerHeight,
                            dataRows: document.querySelectorAll('.c-table-row-body, [class*="row"], tr').length
                        }};
                    }} else {{
                        scrollInfo = {{
                            scrollTop: container.scrollTop,
                            scrollHeight: container.scrollHeight,
                            clientHeight: container.clientHeight,
                            dataRows: document.querySelectorAll('.c-table-row-body, [class*="row"], tr').length
                        }};
                    }}
                    
                    // 执行虚拟滚动检测
                    const virtualScrollInfo = detectVirtualScroll();
                    scrollInfo.isVirtualScroll = !!virtualScrollInfo;
                    scrollInfo.virtualScrollDetected = virtualScrollInfo ? virtualScrollInfo.selector : null;
                    
                    if (scrollInfo.isVirtualScroll) {{
                        const virtualGroup = virtualScrollInfo.group;
                        const virtualBefore = virtualScrollInfo.before;
                        
                        // 使用getBoundingClientRect获取准确尺寸
                        const groupRect = virtualGroup.getBoundingClientRect();
                        const beforeRect = virtualBefore.getBoundingClientRect();
                        
                        // 获取计算样式
                        const groupStyle = window.getComputedStyle(virtualGroup);
                        const beforeStyle = window.getComputedStyle(virtualBefore);
                        
                        // 增强虚拟高度获取
                        let virtualGroupHeight = 0;
                        if (groupRect.height > 0) {{
                            virtualGroupHeight = groupRect.height;
                        }} else if (groupStyle.height && groupStyle.height !== 'auto') {{
                            virtualGroupHeight = parseFloat(groupStyle.height);
                        }} else if (virtualGroup.offsetHeight > 0) {{
                            virtualGroupHeight = virtualGroup.offsetHeight;
                        }} else {{
                            // 尝试从子元素推断总高度
                            const children = virtualGroup.children;
                            if (children.length > 0) {{
                                virtualGroupHeight = Array.from(children).reduce((total, child) => {{
                                    return total + (child.getBoundingClientRect().height || child.offsetHeight || 0);
                                }}, 0);
                            }}
                        }}
                        
                        scrollInfo.virtualGroupHeight = virtualGroupHeight;
                        
                        // 增强虚拟偏移获取 - 多重策略
                        let virtualOffset = 0;
                        
                        // 策略1: 优先使用transform translateY
                        const transform = beforeStyle.transform;
                        if (transform && transform !== 'none' && transform !== 'matrix(1, 0, 0, 1, 0, 0)') {{
                            const translateYMatch = transform.match(/translateY\(([^)]+)\)/);
                            const matrixMatch = transform.match(/matrix\([^,]+,[^,]+,[^,]+,[^,]+,[^,]+,([^)]+)\)/);
                            
                            if (translateYMatch) {{
                                const translateY = parseFloat(translateYMatch[1].replace('px', ''));
                                if (!isNaN(translateY)) {{
                                    virtualOffset = Math.abs(translateY);
                                }}
                            }} else if (matrixMatch) {{
                                const matrixY = parseFloat(matrixMatch[1]);
                                if (!isNaN(matrixY)) {{
                                    virtualOffset = Math.abs(matrixY);
                                }}
                            }}
                        }}
                        
                        // 策略2: 使用top或marginTop属性
                        if (virtualOffset === 0) {{
                            const top = parseFloat(beforeStyle.top) || 0;
                            const marginTop = parseFloat(beforeStyle.marginTop) || 0;
                            const paddingTop = parseFloat(beforeStyle.paddingTop) || 0;
                            
                            virtualOffset = Math.abs(top) || Math.abs(marginTop) || paddingTop;
                        }}
                        
                        // 策略3: 使用getBoundingClientRect
                        if (virtualOffset === 0 && beforeRect.height > 0) {{
                            virtualOffset = beforeRect.height;
                        }}
                        
                        // 策略4: 使用offsetHeight
                        if (virtualOffset === 0 && virtualBefore.offsetHeight > 0) {{
                            virtualOffset = virtualBefore.offsetHeight;
                        }}
                        
                        scrollInfo.virtualBeforeHeight = virtualOffset;
                        scrollInfo.virtualScrollProgress = virtualOffset;
                        
                        // 计算进度百分比
                        scrollInfo.virtualScrollPercentage = virtualGroupHeight > 0 ? 
                            (virtualOffset / virtualGroupHeight * 100) : 0;
                        
                        // 详细调试信息
                        scrollInfo.debugInfo = {{
                            detectedSelector: virtualScrollInfo.selector,
                            groupRect: {{ width: groupRect.width, height: groupRect.height }},
                            beforeRect: {{ width: beforeRect.width, height: beforeRect.height }},
                            groupStyle: {{ height: groupStyle.height }},
                            beforeStyle: {{ 
                                height: beforeStyle.height, 
                                paddingTop: beforeStyle.paddingTop, 
                                marginTop: beforeStyle.marginTop,
                                top: beforeStyle.top,
                                transform: beforeStyle.transform 
                            }},
                            offsetHeight: {{ group: virtualGroup.offsetHeight, before: virtualBefore.offsetHeight }},
                            calculatedOffset: virtualOffset,
                            calculatedGroupHeight: virtualGroupHeight
                        }};
                    }} else {{
                        scrollInfo.virtualGroupHeight = 0;
                        scrollInfo.virtualBeforeHeight = 0;
                        scrollInfo.virtualScrollProgress = 0;
                        scrollInfo.virtualScrollPercentage = 0;
                        scrollInfo.debugInfo = {{ message: '未检测到虚拟滚动元素' }};
                    }}
                    
                    return scrollInfo;
                }}
                """
                initial_info = await self.page.evaluate(initial_scroll_script)
                previous_scroll_position = initial_info['scrollTop']
                
                virtual_info = "" if not initial_info.get('isVirtualScroll') else f", 虚拟滚动[总高度={initial_info.get('virtualGroupHeight', 0):.1f}, 当前偏移={initial_info.get('virtualBeforeHeight', 0):.1f}, 进度={initial_info.get('virtualScrollPercentage', 0):.1f}%]"
                
                # 输出详细调试信息（增强版）
                if initial_info.get('isVirtualScroll'):
                    debug_info = initial_info.get('debugInfo', {})
                    detected_selector = debug_info.get('detectedSelector', {})
                    self.logger.info(f"🎯 检测到虚拟滚动！选择器: {detected_selector.get('group', '未知')} + {detected_selector.get('before', '未知')}")
                    self.logger.debug(f"📏 虚拟滚动详细信息: "
                                    f"总高度={debug_info.get('calculatedGroupHeight', 0):.1f}px, "
                                    f"当前偏移={debug_info.get('calculatedOffset', 0):.1f}px, "
                                    f"进度={initial_info.get('virtualScrollPercentage', 0):.1f}%")
                    self.logger.debug(f"🔍 调试数据: groupRect={debug_info.get('groupRect')}, "
                                    f"beforeStyle.transform={debug_info.get('beforeStyle', {}).get('transform', 'none')}")
                else:
                    debug_msg = initial_info.get('debugInfo', {}).get('message', '检测失败')
                    self.logger.info(f"⚠️ 未检测到虚拟滚动: {debug_msg}")
                
                self.logger.info(f"初始滚动信息: scrollTop={initial_info['scrollTop']}, "
                               f"scrollHeight={initial_info['scrollHeight']}, "
                               f"clientHeight={initial_info['clientHeight']}, "
                               f"数据行数={initial_info['dataRows']}{virtual_info}")
                
                if progress_callback and show_progress:
                    scroll_type = "虚拟滚动" if initial_info.get('isVirtualScroll') else "普通滚动"
                    message = f"检测到滚动容器（{scroll_type}），初始数据行数: {initial_info['dataRows']}"
                    progress_callback(scroll_count, initial_info['dataRows'], message)
                    
            except Exception as e:
                self.logger.warning(f"获取初始滚动信息失败: {e}")
                initial_info = {'scrollTop': 0, 'scrollHeight': 1000, 'clientHeight': 800, 'dataRows': 0}
                previous_scroll_position = 0
            
            # 开始滚动循环
            while scroll_count < max_scroll_attempts:
                # 检查超时
                current_time = asyncio.get_event_loop().time()
                if current_time - start_time > loading_timeout:
                    self.logger.warning(f"滚动加载超时 ({loading_timeout}秒)，停止滚动")
                    break
                
                try:
                    # 增强网络请求监听（支持动态数据加载检测）
                    network_response_promise = None
                    xhr_promise = None
                    
                    if network_monitoring_enabled:
                        # 设置多重网络请求监听策略
                        try:
                            # 策略1: API响应监听（扩展模式匹配）
                            enhanced_api_patterns = api_patterns + [
                                '**/stock**',
                                '**/list**',
                                '**/table**',
                                '**/virtual**',
                                '**/scroll**',
                                '**/fetch**',
                                '**/query**',
                                '**/*data*',
                                '**/*json*',
                                '**/*ajax*',
                                '**/api/v*/stock*',
                                '**/topxlc.com/**'
                            ]
                            
                            def is_data_response(response):
                                """判断是否为数据响应"""
                                try:
                                    url = response.url.lower()
                                    content_type = response.headers.get('content-type', '').lower()
                                    
                                    # URL模式匹配
                                    for pattern in enhanced_api_patterns:
                                        if pattern.replace('**', '').replace('*', '') in url:
                                            return True
                                    
                                    # 内容类型匹配
                                    if any(ct in content_type for ct in ['json', 'javascript', 'text/plain']):
                                        return True
                                    
                                    # 状态码检查
                                    return response.status == 200
                                except:
                                    return False
                            
                            network_response_promise = self.page.wait_for_response(
                                is_data_response,
                                timeout=single_scroll_timeout * 1000
                            )
                            
                            # 策略2: XHR/Fetch请求监听（通过页面脚本注入）
                            xhr_monitor_script = """
                            () => {
                                return new Promise((resolve) => {
                                    let requestCount = 0;
                                    let responseCount = 0;
                                    
                                    // 监听 XMLHttpRequest
                                    const originalXHROpen = XMLHttpRequest.prototype.open;
                                    const originalXHRSend = XMLHttpRequest.prototype.send;
                                    
                                    XMLHttpRequest.prototype.open = function(method, url) {
                                        this._url = url;
                                        return originalXHROpen.apply(this, arguments);
                                    };
                                    
                                    XMLHttpRequest.prototype.send = function() {
                                        const xhr = this;
                                        const url = xhr._url || '';
                                        
                                        if (url.includes('stock') || url.includes('data') || url.includes('list')) {
                                            requestCount++;
                                            
                                            xhr.addEventListener('readystatechange', function() {
                                                if (xhr.readyState === 4) {
                                                    responseCount++;
                                                    if (responseCount >= requestCount) {
                                                        resolve({ type: 'xhr', url: url, status: xhr.status });
                                                    }
                                                }
                                            });
                                        }
                                        
                                        return originalXHRSend.apply(this, arguments);
                                    };
                                    
                                    // 监听 Fetch API
                                    const originalFetch = window.fetch;
                                    window.fetch = function(url, options) {
                                        const urlStr = url.toString();
                                        
                                        if (urlStr.includes('stock') || urlStr.includes('data') || urlStr.includes('list')) {
                                            requestCount++;
                                            
                                            return originalFetch.apply(this, arguments)
                                                .then(response => {
                                                    responseCount++;
                                                    if (responseCount >= requestCount) {
                                                        resolve({ type: 'fetch', url: urlStr, status: response.status });
                                                    }
                                                    return response;
                                                })
                                                .catch(error => {
                                                    responseCount++;
                                                    resolve({ type: 'fetch', url: urlStr, error: error.message });
                                                    throw error;
                                                });
                                        }
                                        
                                        return originalFetch.apply(this, arguments);
                                    };
                                    
                                    // 超时处理
                                    setTimeout(() => {
                                        resolve({ type: 'timeout', message: '网络监听超时' });
                                    }, """ + str(single_scroll_timeout * 1000) + """);
                                });
                            }
                            """
                            
                            xhr_promise = self.page.evaluate(xhr_monitor_script)
                            
                        except Exception as monitor_error:
                            self.logger.debug(f"设置网络监听失败: {monitor_error}")
                            network_response_promise = None
                            xhr_promise = None
                    
                    # 执行滚动（支持增强虚拟滚动检测）
                    scroll_script = f"""
                    () => {{
                        const container = {container_script};
                        const scrollDistance = {scroll_distance};
                        
                        // 复用增强虚拟滚动检测函数
                        function detectVirtualScroll() {{
                            const virtualSelectors = [
                                {{ group: '.c-virtual-group', before: '.c-virtual-before' }},
                                {{ group: '.virtual-group', before: '.virtual-before' }},
                                {{ group: '[class*="virtual"]', before: '[class*="virtual"][class*="before"]' }},
                                {{ group: '.ant-virtual-list', before: '.ant-virtual-list-holder-inner' }},
                                {{ group: '.el-virtual-list', before: '.el-virtual-list-item' }},
                                {{ group: '[data-virtual="true"]', before: '[data-virtual-before]' }}
                            ];
                            
                            for (const selector of virtualSelectors) {{
                                const groupEl = document.querySelector(selector.group);
                                const beforeEl = document.querySelector(selector.before);
                                
                                if (groupEl && beforeEl) {{
                                    return {{ group: groupEl, before: beforeEl, selector: selector }};
                                }}
                            }}
                            
                            // 智能检测虚拟滚动
                            const allElements = document.querySelectorAll('*');
                            for (const el of allElements) {{
                                const classList = Array.from(el.classList);
                                const hasVirtualClass = classList.some(cls => 
                                    cls.includes('virtual') || cls.includes('scroll') || cls.includes('list-container')
                                );
                                
                                if (hasVirtualClass && el.children.length > 0) {{
                                    const beforeEl = Array.from(el.children).find(child => {{
                                        const childClasses = Array.from(child.classList);
                                        return childClasses.some(cls => 
                                            cls.includes('before') || cls.includes('offset') || child.style.transform
                                        );
                                    }});
                                    
                                    if (beforeEl) {{
                                        return {{ group: el, before: beforeEl, selector: {{ group: '智能检测', before: '智能检测' }} }};
                                    }}
                                }}
                            }}
                            
                            return null;
                        }}
                        
                        // 执行滚动
                        let scrollInfo;
                        if (container === window) {{
                            window.scrollBy(0, scrollDistance);
                            scrollInfo = {{
                                scrollTop: window.pageYOffset || document.documentElement.scrollTop,
                                scrollHeight: document.documentElement.scrollHeight,
                                clientHeight: window.innerHeight,
                                dataRows: document.querySelectorAll('.c-table-row-body, [class*="row"], tr').length
                            }};
                        }} else {{
                            container.scrollTop += scrollDistance;
                            scrollInfo = {{
                                scrollTop: container.scrollTop,
                                scrollHeight: container.scrollHeight,
                                clientHeight: container.clientHeight,
                                dataRows: document.querySelectorAll('.c-table-row-body, [class*="row"], tr').length
                            }};
                        }}
                        
                        // 执行虚拟滚动检测
                        const virtualScrollInfo = detectVirtualScroll();
                        scrollInfo.isVirtualScroll = !!virtualScrollInfo;
                        
                        if (scrollInfo.isVirtualScroll) {{
                            const virtualGroup = virtualScrollInfo.group;
                            const virtualBefore = virtualScrollInfo.before;
                            
                            // 获取准确的虚拟滚动尺寸
                            const groupRect = virtualGroup.getBoundingClientRect();
                            const beforeRect = virtualBefore.getBoundingClientRect();
                            const groupStyle = window.getComputedStyle(virtualGroup);
                            const beforeStyle = window.getComputedStyle(virtualBefore);
                            
                            // 计算虚拟总高度
                            let virtualGroupHeight = 0;
                            if (groupRect.height > 0) {{
                                virtualGroupHeight = groupRect.height;
                            }} else if (groupStyle.height && groupStyle.height !== 'auto') {{
                                virtualGroupHeight = parseFloat(groupStyle.height);
                            }} else if (virtualGroup.offsetHeight > 0) {{
                                virtualGroupHeight = virtualGroup.offsetHeight;
                            }}
                            
                            scrollInfo.virtualGroupHeight = virtualGroupHeight;
                            
                            // 增强虚拟偏移计算
                            let virtualOffset = 0;
                            
                            // 策略1: transform translateY (最准确)
                            const transform = beforeStyle.transform;
                            if (transform && transform !== 'none' && transform !== 'matrix(1, 0, 0, 1, 0, 0)') {{
                                const translateYMatch = transform.match(/translateY\(([^)]+)\)/);
                                const matrixMatch = transform.match(/matrix\([^,]+,[^,]+,[^,]+,[^,]+,[^,]+,([^)]+)\)/);
                                
                                if (translateYMatch) {{
                                    const translateY = parseFloat(translateYMatch[1].replace('px', ''));
                                    if (!isNaN(translateY)) {{
                                        virtualOffset = Math.abs(translateY);
                                    }}
                                }} else if (matrixMatch) {{
                                    const matrixY = parseFloat(matrixMatch[1]);
                                    if (!isNaN(matrixY)) {{
                                        virtualOffset = Math.abs(matrixY);
                                    }}
                                }}
                            }}
                            
                            // 策略2: 位置属性
                            if (virtualOffset === 0) {{
                                const top = parseFloat(beforeStyle.top) || 0;
                                const marginTop = parseFloat(beforeStyle.marginTop) || 0;
                                virtualOffset = Math.abs(top) || Math.abs(marginTop);
                            }}
                            
                            // 策略3: 几何属性
                            if (virtualOffset === 0) {{
                                virtualOffset = beforeRect.height || virtualBefore.offsetHeight || 0;
                            }}
                            
                            scrollInfo.virtualBeforeHeight = virtualOffset;
                            scrollInfo.virtualScrollProgress = virtualOffset;
                            scrollInfo.virtualScrollPercentage = virtualGroupHeight > 0 ? 
                                (virtualOffset / virtualGroupHeight * 100) : 0;
                        }} else {{
                            scrollInfo.virtualGroupHeight = 0;
                            scrollInfo.virtualBeforeHeight = 0;
                            scrollInfo.virtualScrollProgress = 0;
                            scrollInfo.virtualScrollPercentage = 0;
                        }}
                        
                        return scrollInfo;
                    }}
                    """
                    
                    scroll_info = await asyncio.wait_for(
                        self.page.evaluate(scroll_script),
                        timeout=single_scroll_timeout
                    )
                    
                    scroll_count += 1
                    current_scroll_position = scroll_info.get('scrollTop', 0)
                    current_virtual_progress = scroll_info.get('virtualScrollProgress', 0)
                    
                    debug_info = f"scrollTop={current_scroll_position}, scrollHeight={scroll_info.get('scrollHeight', 0)}, 数据行数={scroll_info.get('dataRows', 0)}"
                    if scroll_info.get('isVirtualScroll'):
                        debug_info += f", 虚拟偏移={current_virtual_progress}px, 进度={scroll_info.get('virtualScrollPercentage', 0):.1f}%"
                    
                    self.logger.debug(f"第{scroll_count}次滚动完成: {debug_info}")
                    
                    # 等待滚动后的数据加载
                    await asyncio.sleep(scroll_wait_time)
                    
                    # 增强网络请求监听处理（多重策略）
                    if network_monitoring_enabled and (network_response_promise or xhr_promise):
                        try:
                            # 等待任意一种网络请求完成
                            network_tasks = []
                            if network_response_promise:
                                network_tasks.append(asyncio.create_task(network_response_promise))
                            if xhr_promise:
                                network_tasks.append(asyncio.create_task(xhr_promise))
                            
                            if network_tasks:
                                # 等待任意网络请求完成
                                done, pending = await asyncio.wait(
                                    network_tasks,
                                    timeout=data_change_wait,
                                    return_when=asyncio.FIRST_COMPLETED
                                )
                                
                                # 取消未完成的任务
                                for task in pending:
                                    task.cancel()
                                
                                # 处理完成的任务
                                if done:
                                    for task in done:
                                        try:
                                            result = await task
                                            if hasattr(result, 'url'):
                                                self.logger.debug(f"✅ 第{scroll_count}次滚动检测到网络响应: {result.url}")
                                            elif isinstance(result, dict):
                                                self.logger.debug(f"✅ 第{scroll_count}次滚动检测到{result.get('type', 'unknown')}请求: {result.get('url', 'unknown')}")
                                        except Exception as task_error:
                                            self.logger.debug(f"网络任务处理失败: {task_error}")
                                else:
                                    self.logger.debug(f"⏰ 第{scroll_count}次滚动后网络请求超时")
                            
                        except asyncio.TimeoutError:
                            self.logger.debug(f"⏰ 第{scroll_count}次滚动后网络请求监听超时")
                        except Exception as e:
                            self.logger.debug(f"❌ 第{scroll_count}次滚动后网络请求监听异常: {e}")
                    
                    # 传统网络空闲等待（作为备用策略）
                    try:
                        await self.page.wait_for_load_state('networkidle', timeout=data_change_wait * 1000)
                        self.logger.debug(f"🌐 第{scroll_count}次滚动后网络空闲状态检测完成")
                    except:
                        # 如果没有网络请求或超时，继续执行
                        pass
                    
                    # DOM变化监听（确保数据更新完成）
                    try:
                        dom_change_script = """
                        () => {
                            return new Promise((resolve) => {
                                const targetNode = document.querySelector('.c-table-row-body') || document.body;
                                let changeCount = 0;
                                
                                const observer = new MutationObserver((mutations) => {
                                    changeCount += mutations.length;
                                    if (changeCount >= 5) {  // 检测到足够的DOM变化
                                        observer.disconnect();
                                        resolve({ changes: changeCount, type: 'dom_change' });
                                    }
                                });
                                
                                observer.observe(targetNode, {
                                    childList: true,
                                    subtree: true,
                                    attributes: false
                                });
                                
                                // 超时处理
                                setTimeout(() => {
                                    observer.disconnect();
                                    resolve({ changes: changeCount, type: 'timeout' });
                                }, """ + str(int(data_change_wait * 1000)) + """);
                            });
                        }
                        """
                        
                        dom_result = await asyncio.wait_for(
                            self.page.evaluate(dom_change_script),
                            timeout=data_change_wait + 1
                        )
                        
                        if dom_result.get('changes', 0) > 0:
                            self.logger.debug(f"🔄 第{scroll_count}次滚动后检测到{dom_result['changes']}个DOM变化")
                        
                    except Exception as dom_error:
                        self.logger.debug(f"DOM变化监听失败: {dom_error}")
                    
                    # 再等待一段时间确保DOM更新
                    await asyncio.sleep(data_change_wait)
                    
                    # 重新获取滚动信息（可能因为内容加载导致scrollHeight或虚拟元素变化）
                    updated_scroll_script = f"""
                    () => {{
                        const container = {container_script};
                        
                        // 复用增强虚拟滚动检测函数
                        function detectVirtualScroll() {{
                            const virtualSelectors = [
                                {{ group: '.c-virtual-group', before: '.c-virtual-before' }},
                                {{ group: '.virtual-group', before: '.virtual-before' }},
                                {{ group: '[class*="virtual"]', before: '[class*="virtual"][class*="before"]' }},
                                {{ group: '.ant-virtual-list', before: '.ant-virtual-list-holder-inner' }},
                                {{ group: '.el-virtual-list', before: '.el-virtual-list-item' }},
                                {{ group: '[data-virtual="true"]', before: '[data-virtual-before]' }}
                            ];
                            
                            for (const selector of virtualSelectors) {{
                                const groupEl = document.querySelector(selector.group);
                                const beforeEl = document.querySelector(selector.before);
                                
                                if (groupEl && beforeEl) {{
                                    return {{ group: groupEl, before: beforeEl, selector: selector }};
                                }}
                            }}
                            
                            const allElements = document.querySelectorAll('*');
                            for (const el of allElements) {{
                                const classList = Array.from(el.classList);
                                const hasVirtualClass = classList.some(cls => 
                                    cls.includes('virtual') || cls.includes('scroll') || cls.includes('list-container')
                                );
                                
                                if (hasVirtualClass && el.children.length > 0) {{
                                    const beforeEl = Array.from(el.children).find(child => {{
                                        const childClasses = Array.from(child.classList);
                                        return childClasses.some(cls => 
                                            cls.includes('before') || cls.includes('offset') || child.style.transform
                                        );
                                    }});
                                    
                                    if (beforeEl) {{
                                        return {{ group: el, before: beforeEl, selector: {{ group: '智能检测', before: '智能检测' }} }};
                                    }}
                                }}
                            }}
                            
                            return null;
                        }}
                        
                        let scrollInfo;
                        if (container === window) {{
                            scrollInfo = {{
                                scrollTop: window.pageYOffset || document.documentElement.scrollTop,
                                scrollHeight: document.documentElement.scrollHeight,
                                clientHeight: window.innerHeight,
                                dataRows: document.querySelectorAll('.c-table-row-body, [class*="row"], tr').length
                            }};
                        }} else {{
                            scrollInfo = {{
                                scrollTop: container.scrollTop,
                                scrollHeight: container.scrollHeight,
                                clientHeight: container.clientHeight,
                                dataRows: document.querySelectorAll('.c-table-row-body, [class*="row"], tr').length
                            }};
                        }}
                        
                        // 虚拟滚动状态检测（增强版）
                        const virtualScrollInfo = detectVirtualScroll();
                        scrollInfo.isVirtualScroll = !!virtualScrollInfo;
                        
                        if (scrollInfo.isVirtualScroll) {{
                            const virtualGroup = virtualScrollInfo.group;
                            const virtualBefore = virtualScrollInfo.before;
                            
                            // 获取准确的虚拟滚动尺寸  
                            const groupRect = virtualGroup.getBoundingClientRect();
                            const beforeRect = virtualBefore.getBoundingClientRect();
                            const groupStyle = window.getComputedStyle(virtualGroup);
                            const beforeStyle = window.getComputedStyle(virtualBefore);
                            
                            // 计算虚拟总高度
                            let virtualGroupHeight = 0;
                            if (groupRect.height > 0) {{
                                virtualGroupHeight = groupRect.height;
                            }} else if (groupStyle.height && groupStyle.height !== 'auto') {{
                                virtualGroupHeight = parseFloat(groupStyle.height);
                            }} else if (virtualGroup.offsetHeight > 0) {{
                                virtualGroupHeight = virtualGroup.offsetHeight;
                            }}
                            
                            scrollInfo.virtualGroupHeight = virtualGroupHeight;
                            
                            // 增强虚拟偏移计算
                            let virtualOffset = 0;
                            
                            // 策略1: transform translateY (最准确)
                            const transform = beforeStyle.transform;
                            if (transform && transform !== 'none' && transform !== 'matrix(1, 0, 0, 1, 0, 0)') {{
                                const translateYMatch = transform.match(/translateY\(([^)]+)\)/);
                                const matrixMatch = transform.match(/matrix\([^,]+,[^,]+,[^,]+,[^,]+,[^,]+,([^)]+)\)/);
                                
                                if (translateYMatch) {{
                                    const translateY = parseFloat(translateYMatch[1].replace('px', ''));
                                    if (!isNaN(translateY)) {{
                                        virtualOffset = Math.abs(translateY);
                                    }}
                                }} else if (matrixMatch) {{
                                    const matrixY = parseFloat(matrixMatch[1]);
                                    if (!isNaN(matrixY)) {{
                                        virtualOffset = Math.abs(matrixY);
                                    }}
                                }}
                            }}
                            
                            // 策略2: 位置属性
                            if (virtualOffset === 0) {{
                                const top = parseFloat(beforeStyle.top) || 0;
                                const marginTop = parseFloat(beforeStyle.marginTop) || 0;
                                virtualOffset = Math.abs(top) || Math.abs(marginTop);
                            }}
                            
                            // 策略3: 几何属性
                            if (virtualOffset === 0) {{
                                virtualOffset = beforeRect.height || virtualBefore.offsetHeight || 0;
                            }}
                            
                            scrollInfo.virtualBeforeHeight = virtualOffset;
                            scrollInfo.virtualScrollProgress = virtualOffset;
                            scrollInfo.virtualScrollPercentage = virtualGroupHeight > 0 ? 
                                (virtualOffset / virtualGroupHeight * 100) : 0;
                        }} else {{
                            scrollInfo.virtualGroupHeight = 0;
                            scrollInfo.virtualBeforeHeight = 0;
                            scrollInfo.virtualScrollProgress = 0;
                            scrollInfo.virtualScrollPercentage = 0;
                        }}
                        
                        return scrollInfo;
                    }}
                    """
                    
                    updated_scroll_info = await asyncio.wait_for(
                        self.page.evaluate(updated_scroll_script),
                        timeout=single_scroll_timeout
                    )
                    
                    # 检查滚动位置是否发生变化（虚拟滚动的主要判断依据）
                    is_virtual_scroll = updated_scroll_info.get('isVirtualScroll', False)
                    if is_virtual_scroll:
                        # 虚拟滚动模式：监控virtual-before元素的高度变化
                        previous_virtual_progress = getattr(self, '_previous_virtual_progress', 0)
                        current_virtual_progress = updated_scroll_info.get('virtualScrollProgress', 0)
                        
                        scroll_position_changed = abs(current_virtual_progress - previous_virtual_progress) > position_change_threshold
                        
                        # 检查是否已经滚动到虚拟列表的底部
                        virtual_total_height = updated_scroll_info.get('virtualGroupHeight', 0)
                        virtual_current_offset = updated_scroll_info.get('virtualBeforeHeight', 0)
                        virtual_visible_height = updated_scroll_info.get('clientHeight', 800)
                        
                        # 估算剩余可滚动的虚拟内容高度
                        remaining_virtual_content = virtual_total_height - (virtual_current_offset + virtual_visible_height)
                        is_near_bottom = remaining_virtual_content < bottom_threshold
                        
                        self.logger.debug(f"虚拟滚动检查: 偏移变化={scroll_position_changed}, "
                                        f"当前偏移={virtual_current_offset}, 总高度={virtual_total_height}, "
                                        f"剩余内容={remaining_virtual_content}, 接近底部={is_near_bottom}")
                        
                        # 更新上次的虚拟进度
                        self._previous_virtual_progress = current_virtual_progress
                    else:
                        # 普通滚动模式：使用原有逻辑
                        if virtual_scroll_enabled:
                            scroll_position_changed = abs(updated_scroll_info['scrollTop'] - previous_scroll_position) > position_change_threshold
                            # 使用配置的底部阈值
                            scroll_bottom_distance = updated_scroll_info['scrollHeight'] - (updated_scroll_info['scrollTop'] + updated_scroll_info['clientHeight'])
                            is_near_bottom = scroll_bottom_distance < bottom_threshold
                        else:
                            # 传统滚动检测
                            scroll_position_changed = abs(updated_scroll_info['scrollTop'] - previous_scroll_position) > 10
                            scroll_bottom_distance = updated_scroll_info['scrollHeight'] - (updated_scroll_info['scrollTop'] + updated_scroll_info['clientHeight'])
                            is_near_bottom = scroll_bottom_distance < 100
                    
                    scroll_mode = "虚拟" if is_virtual_scroll else "普通"
                    distance_info = f"剩余内容={remaining_virtual_content if is_virtual_scroll else scroll_bottom_distance}" if 'remaining_virtual_content' in locals() or 'scroll_bottom_distance' in locals() else "无距离信息"
                    self.logger.debug(f"{scroll_mode}滚动检查: 位置变化={scroll_position_changed}, "
                                    f"{distance_info}, "
                                    f"接近底部={is_near_bottom}")
                    
                    # 增强进度回调 - 去除频率限制，每次滚动都更新
                    if progress_callback and show_progress:
                        try:
                            # 构建详细的进度信息
                            if is_virtual_scroll:
                                virtual_percent = updated_scroll_info.get('virtualScrollPercentage', 0)
                                virtual_offset = updated_scroll_info.get('virtualBeforeHeight', 0)
                                virtual_total = updated_scroll_info.get('virtualGroupHeight', 0)
                                
                                # 虚拟滚动专用进度信息
                                progress_info = f"🔄 虚拟滚动进度: {virtual_percent:.1f}%"
                                detail_info = f"偏移量: {virtual_offset:.0f}px / 总高度: {virtual_total:.0f}px"
                                
                                message = f"第{scroll_count}次滚动，{progress_info} ({detail_info})，数据行数: {updated_scroll_info['dataRows']}"
                                
                                # 为虚拟滚动添加更详细的状态信息
                                if virtual_percent > 90:
                                    message += " 🔥 接近完成！"
                                elif virtual_percent > 50:
                                    message += " ⚡ 进度过半"
                                elif virtual_percent > 0:
                                    message += " 🚀 滚动中..."
                                else:
                                    message += " ⚠️ 进度计算中..."
                            else:
                                # 普通滚动信息
                                scroll_position = updated_scroll_info['scrollTop']
                                scroll_height = updated_scroll_info.get('scrollHeight', 0)
                                progress_percent = (scroll_position / scroll_height * 100) if scroll_height > 0 else 0
                                
                                message = f"第{scroll_count}次滚动，位置: {scroll_position}px ({progress_percent:.1f}%)，数据行数: {updated_scroll_info['dataRows']}"
                            
                            # 每次滚动都执行回调（去除频率限制）
                            progress_callback(scroll_count, updated_scroll_info['dataRows'], message)
                            
                        except Exception as callback_error:
                            # 进度回调出错不应该影响数据抓取流程，但要记录详细信息
                            self.logger.warning(f"🚨 进度回调执行失败: {callback_error}")
                            # 继续滚动流程，不中断
                    
                    # 多信号综合判断滚动完成
                    completion_signals = {
                        'position_stable': not scroll_position_changed,
                        'near_bottom': is_near_bottom,
                        'data_rows_unchanged': updated_scroll_info['dataRows'] == getattr(self, '_previous_data_rows', 0),
                        'virtual_scroll_complete': False
                    }
                    
                    # 虚拟滚动专用判断
                    if is_virtual_scroll:
                        virtual_progress = updated_scroll_info.get('virtualScrollPercentage', 0)
                        completion_signals['virtual_scroll_complete'] = virtual_progress > 95.0
                        
                        self.logger.debug(f"虚拟滚动综合信号: 进度={virtual_progress:.1f}%, "
                                        f"信号={completion_signals}")
                    
                    # 记录上一次的数据行数
                    self._previous_data_rows = updated_scroll_info['dataRows']
                    
                    # 判断是否完成加载（针对虚拟滚动的优化判断逻辑）
                    if is_virtual_scroll:
                        # 虚拟滚动模式：更宽松的综合判断
                        virtual_progress = updated_scroll_info.get('virtualScrollPercentage', 0)
                        
                        # 主要完成信号
                        primary_signals = [
                            virtual_progress > 98.0,  # 虚拟滚动进度超过98%
                            completion_signals['near_bottom'],  # 接近底部
                        ]
                        
                        # 辅助完成信号
                        secondary_signals = [
                            completion_signals['position_stable'],  # 位置稳定
                            stable_count >= 5,  # 减少稳定检查次数要求
                            completion_signals['data_rows_unchanged']  # 数据行数未变化
                        ]
                        
                        primary_met = sum(primary_signals)
                        secondary_met = sum(secondary_signals)
                        
                        # 满足至少1个主要信号且2个辅助信号，或者满足2个主要信号
                        if (primary_met >= 1 and secondary_met >= 2) or primary_met >= 2:
                            self.logger.info(f"虚拟滚动数据加载完成：主要信号{primary_met}/2，辅助信号{secondary_met}/3，进度{virtual_progress:.1f}%")
                            break
                        elif not scroll_position_changed or is_near_bottom:
                            stable_count += 1
                            self.logger.debug(f"虚拟滚动稳定计数: {stable_count}，进度: {virtual_progress:.1f}%")
                        else:
                            stable_count = 0
                    else:
                        # 传统滚动模式
                        if not scroll_position_changed or is_near_bottom:
                            stable_count += 1
                            self.logger.debug(f"传统滚动稳定计数: {stable_count}")
                        else:
                            stable_count = 0
                            if not is_virtual_scroll:
                                previous_scroll_position = updated_scroll_info['scrollTop']
                        
                        # 传统判断
                        if stable_count >= stable_check_count and is_near_bottom:
                            self.logger.info(f"传统滚动数据加载完成：连续{stable_count}次滚动位置稳定且已到达底部")
                            break
                    
                    # 虚拟滚动优化：如果接近底部但还未完全稳定，尝试强制滚动到底部
                    if is_virtual_scroll and virtual_scroll_enabled and is_near_bottom and stable_count >= 2:
                        if scroll_count % force_bottom_attempts == 0:
                            self.logger.info("虚拟滚动优化：尝试强制滚动到底部")
                            # 虚拟滚动模式下，尝试滚动更多距离
                            force_virtual_scroll_script = f"""
                            () => {{
                                const container = {container_script};
                                const largeScrollDistance = {scroll_distance * 3};
                                
                                if (container === window) {{
                                    // 尝试多次大距离滚动
                                    for (let i = 0; i < 5; i++) {{
                                        window.scrollBy(0, largeScrollDistance);
                                    }}
                                }} else {{
                                    // 尝试多次大距离滚动
                                    for (let i = 0; i < 5; i++) {{
                                        container.scrollTop += largeScrollDistance;
                                    }}
                                }}
                                return true;
                            }}
                            """
                            await self.page.evaluate(force_virtual_scroll_script)
                            await asyncio.sleep(scroll_wait_time * 2)  # 增加等待时间
                    elif virtual_scroll_enabled and is_near_bottom and stable_count >= 2:
                        if scroll_count % force_bottom_attempts == 0:
                            self.logger.info("普通滚动优化：尝试强制滚动到底部")
                            # 强制滚动到最底部
                            force_bottom_script = f"""
                            () => {{
                                const container = {container_script};
                                if (container === window) {{
                                    window.scrollTo(0, document.documentElement.scrollHeight);
                                }} else {{
                                    container.scrollTop = container.scrollHeight;
                                }}
                                return true;
                            }}
                            """
                            await self.page.evaluate(force_bottom_script)
                            await asyncio.sleep(scroll_wait_time)
                    
                    # 早期退出检查（防止过度滚动）
                    if is_near_bottom and stable_count >= max(2, stable_check_count // 2):
                        if is_virtual_scroll:
                            virtual_progress = updated_scroll_info.get('virtualScrollPercentage', 0)
                            if virtual_progress > 90.0:  # 虚拟滚动进度超过90%
                                self.logger.info(f"虚拟滚动达到{virtual_progress:.1f}%且接近底部，停止滚动")
                                break
                        else:
                            self.logger.info("已到达页面底部且滚动基本稳定，停止滚动")
                            break
                        
                # 智能异常处理（区分浏览器关闭和其他错误）
                except asyncio.TimeoutError:
                    self.logger.warning(f"⏰ 第{scroll_count}次滚动超时，继续尝试")
                    continue
                except Exception as e:
                    error_msg = str(e).lower()
                    
                    # 专门处理浏览器关闭异常（静默处理）
                    if any(keyword in error_msg for keyword in [
                        'target page, context or browser has been closed',
                        'browser has been closed',
                        'context has been closed',
                        'page has been closed',
                        'target closed',
                        'connection closed',
                        'browser context is closed'
                    ]):
                        self.logger.info("🔚 检测到浏览器已关闭，停止滚动")
                        break  # 静默退出，不显示错误
                    
                    # 处理网络相关异常
                    elif any(keyword in error_msg for keyword in [
                        'network error',
                        'connection refused',
                        'timeout',
                        'net::err_'
                    ]):
                        self.logger.warning(f"🌐 第{scroll_count}次滚动网络异常，稍后重试: {e}")
                        await asyncio.sleep(1)  # 短暂等待后重试
                        continue
                    
                    # 处理页面导航异常
                    elif any(keyword in error_msg for keyword in [
                        'navigation',
                        'page crashed',
                        'renderer process'
                    ]):
                        self.logger.warning(f"📄 第{scroll_count}次滚动页面异常: {e}")
                        continue
                    
                    # 其他未知异常
                    else:
                        self.logger.error(f"❌ 第{scroll_count}次滚动出错: {e}")
                        # 对未知异常仍保持容错，继续尝试
                        continue
            
            # 最终统计
            end_time = asyncio.get_event_loop().time()
            total_time = end_time - start_time
            
            # 获取最终的数据行数（虚拟滚动中可能是固定数量）
            try:
                final_info_script = """
                () => {
                    const virtualGroup = document.querySelector('.c-virtual-group');
                    const virtualBefore = document.querySelector('.c-virtual-before');
                    
                    const result = {
                        dataRows: document.querySelectorAll('.c-table-row-body').length,
                        isVirtualScroll: !!(virtualGroup && virtualBefore)
                    };
                    
                    if (result.isVirtualScroll) {
                        // 使用getBoundingClientRect获取准确尺寸
                        const groupRect = virtualGroup.getBoundingClientRect();
                        const beforeRect = virtualBefore.getBoundingClientRect();
                        
                        // 获取计算样式
                        const groupStyle = window.getComputedStyle(virtualGroup);
                        const beforeStyle = window.getComputedStyle(virtualBefore);
                        
                        // 多种方式获取虚拟高度
                        result.virtualGroupHeight = 
                            groupRect.height || 
                            parseFloat(groupStyle.height) || 
                            virtualGroup.offsetHeight || 0;
                        
                        // 多种方式获取虚拟偏移
                        result.virtualBeforeHeight = 
                            beforeRect.height || 
                            parseFloat(beforeStyle.height) || 
                            parseFloat(beforeStyle.paddingTop) || 
                            virtualBefore.offsetHeight || 0;
                        
                        // 检查CSS transform
                        const transform = beforeStyle.transform;
                        if (transform && transform !== 'none') {
                            const translateYMatch = transform.match(/translateY\\(([^)]+)\\)/);
                            if (translateYMatch) {
                                const translateY = parseFloat(translateYMatch[1]);
                                if (!isNaN(translateY)) {
                                    result.virtualBeforeHeight = Math.abs(translateY);
                                }
                            }
                        }
                        
                        result.virtualScrollPercentage = result.virtualGroupHeight > 0 ? 
                            (result.virtualBeforeHeight / result.virtualGroupHeight * 100) : 0;
                    }
                    
                    return result;
                }
                """
                final_info = await self.page.evaluate(final_info_script)
                final_row_count = final_info.get('dataRows', 0)
                
                # 构建最终消息
                if final_info.get('isVirtualScroll'):
                    scroll_info = f"虚拟滚动进度{final_info.get('virtualScrollPercentage', 0):.1f}%"
                    final_message = f"虚拟滚动加载完成：共滚动{scroll_count}次，{scroll_info}，DOM中{final_row_count}条显示数据，耗时{total_time:.1f}秒"
                else:
                    final_position = updated_scroll_info.get('scrollTop', 0) if 'updated_scroll_info' in locals() else 0
                    final_message = f"滚动加载完成：共滚动{scroll_count}次，最终滚动位置{final_position}，DOM中{final_row_count}条数据，耗时{total_time:.1f}秒"
            except:
                final_info = {'isVirtualScroll': False}
                final_row_count = updated_scroll_info.get('dataRows', 0) if 'updated_scroll_info' in locals() else 0
                final_message = f"滚动加载完成：共滚动{scroll_count}次，最终数据行数{final_row_count}，耗时{total_time:.1f}秒"
            self.logger.info(final_message)
            
            # 最终进度回调
            if progress_callback and show_progress:
                final_status = "虚拟滚动" if final_info.get('isVirtualScroll') else "普通滚动"
                progress_callback(scroll_count, final_row_count, f"{final_status}加载完成")
            
            return True, final_message, final_row_count
            
        except Exception as e:
            error_msg = f"滚动加载过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, 0
    
    async def _detect_scroll_container(self):
        """
        检测页面中的滚动容器
        
        Returns:
            dict: 包含滚动容器信息的字典
        """
        try:
            # 尝试多种策略检测滚动容器
            detect_script = """
            () => {
                // 策略1: 查找常见的滚动容器选择器
                const commonSelectors = [
                    '.table-container',
                    '.scroll-container', 
                    '.data-container',
                    '[class*="scroll"]',
                    '[class*="table"]',
                    '[class*="container"]',
                    '.ant-table-body',
                    '.el-table__body-wrapper',
                    '.c-table'
                ];
                
                for (const selector of commonSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {
                        if (element.scrollHeight > element.clientHeight) {
                            return {
                                found: true,
                                selector: selector,
                                script: `document.querySelector('${selector}')`,
                                description: `容器选择器: ${selector}`
                            };
                        }
                    }
                }
                
                // 策略2: 查找包含表格数据的父容器
                const tableRows = document.querySelectorAll('.c-table-row-body');
                if (tableRows.length > 0) {
                    let parent = tableRows[0].parentElement;
                    while (parent && parent !== document.body) {
                        if (parent.scrollHeight > parent.clientHeight) {
                            const classList = Array.from(parent.classList).join('.');
                            return {
                                found: true,
                                selector: '.' + classList,
                                script: `document.querySelector('.${classList}')`,
                                description: `表格父容器: .${classList}`
                            };
                        }
                        parent = parent.parentElement;
                    }
                }
                
                // 策略3: 查找所有可滚动的元素
                const allElements = document.querySelectorAll('*');
                for (const element of allElements) {
                    const style = window.getComputedStyle(element);
                    if ((style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') 
                        && element.scrollHeight > element.clientHeight) {
                        const classList = Array.from(element.classList).join('.');
                        if (classList) {
                            return {
                                found: true,
                                selector: '.' + classList,
                                script: `document.querySelector('.${classList}')`,
                                description: `可滚动元素: .${classList}`
                            };
                        }
                    }
                }
                
                // 没有找到合适的滚动容器
                return {
                    found: false,
                    selector: null,
                    script: 'window',
                    description: '未找到滚动容器，使用window'
                };
            }
            """
            
            result = await self.page.evaluate(detect_script)
            self.logger.info(f"滚动容器检测结果: {result['description']}")
            return result
            
        except Exception as e:
            self.logger.error(f"检测滚动容器失败: {e}")
            return {
                'found': False,
                'selector': None,
                'script': 'window',
                'description': f'检测失败，使用window: {str(e)}'
            }
    
    async def extract_stock_data(self, progress_callback=None):
        """
        抓取股票数据（包含滚动加载）
        
        Args:
            progress_callback: 进度回调函数，用于GUI更新
        """
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            if not self.is_ready_for_extraction:
                raise Exception("尚未准备好开始数据抓取")
            
            # 等待页面加载完成
            try:
                await self.page.wait_for_load_state('networkidle', timeout=30000)
            except Exception as e:
                self.logger.warning(f"等待页面加载超时: {e}, 继续尝试获取数据")
            
            self.logger.info("开始数据抓取流程...")
            
            # 步骤1: 滚动加载所有数据
            self.logger.info("步骤1: 执行滚动加载，获取所有数据...")
            if progress_callback:
                progress_callback(0, 0, "开始滚动加载数据...")
            
            scroll_success, scroll_message, final_row_count = await self._scroll_and_load_all_data(progress_callback)
            
            if not scroll_success:
                raise Exception(f"滚动加载失败: {scroll_message}")
            
            self.logger.info(f"滚动加载完成: {scroll_message}")
            if progress_callback:
                progress_callback(0, final_row_count, f"滚动加载完成，共{final_row_count}条记录")
            
            # 步骤2: 获取页面HTML内容
            self.logger.info("步骤2: 获取页面HTML内容...")
            if progress_callback:
                progress_callback(0, final_row_count, "正在获取页面HTML内容...")
            
            try:
                html_content = await self.page.content()
                if not html_content or len(html_content.strip()) < 100:
                    raise Exception("获取到的HTML内容为空或过短，请确保页面已正常加载")
            except Exception as e:
                raise Exception(f"获取页面HTML内容失败: {str(e)}")
            
            # 步骤3: 创建HTML数据解析器
            self.logger.info("步骤3: 初始化HTML数据解析器...")
            if progress_callback:
                progress_callback(0, final_row_count, "正在初始化数据解析器...")
            
            try:
                html_parser_manager = create_html_data_parser_manager()
            except Exception as e:
                raise Exception(f"初始化HTML解析器失败: {str(e)}")
            
            # 步骤4: 验证HTML结构
            self.logger.info("步骤4: 验证HTML数据结构...")
            if progress_callback:
                progress_callback(0, final_row_count, "正在验证数据结构...")
            
            validation_result = html_parser_manager.validate_html_structure(html_content)
            
            if not validation_result['is_valid']:
                error_details = []
                if not validation_result.get('header_found'):
                    error_details.append("未找到表头行")
                if not validation_result.get('data_rows_found'):
                    error_details.append("未找到数据行")
                if validation_result.get('missing_columns'):
                    error_details.append(f"缺少必要的数据列: {', '.join(validation_result['missing_columns'])}")
                
                error_msg = f"页面HTML结构验证失败:\n{'\n'.join(error_details)}"
                error_msg += "\n\n可能的原因:\n1. 页面数据尚未加载完成\n2. 页面结构发生变化\n3. 当前页面不是数据列表页面"
                raise Exception(error_msg)
            
            self.logger.info(f"HTML结构验证通过 - 找到{validation_result['total_rows']}行数据，{validation_result['total_columns']}列")
            
            # 步骤5: 提取股票数据
            self.logger.info("步骤5: 解析提取股票数据...")
            if progress_callback:
                progress_callback(0, validation_result['total_rows'], f"正在解析{validation_result['total_rows']}条股票数据...")
            
            try:
                raw_stock_data = html_parser_manager.parse_stock_data_from_html(html_content)
            except Exception as e:
                raise Exception(f"解析股票数据失败: {str(e)}")
            
            if not raw_stock_data:
                raise Exception("未能从页面中提取到股票数据，请确保:\n1. 页面已加载完成\n2. 包含股票数据表格\n3. 表格中有有效的股票信息")
            
            # 步骤6: 转换数据格式
            self.logger.info("步骤6: 转换数据格式...")
            if progress_callback:
                progress_callback(0, len(raw_stock_data), f"正在转换{len(raw_stock_data)}条数据格式...")
            
            extracted_data = []
            invalid_count = 0
            
            for stock in raw_stock_data:
                try:
                    stock_data = {
                        '股票代码': stock.get('stock_code', ''),
                        '股票名称': stock.get('stock_name', ''),
                        '小草竞王': stock.get('jingwang', ''),
                        '小草红盘起爆': stock.get('hongpan_qibao', ''),
                        '小草绿盘低吸': stock.get('lvpan_dixi', ''),
                        '小草连板接力': stock.get('lianban_jieli', '')
                    }
                    # 验证必要字段
                    if stock_data['股票代码'].strip() and stock_data['股票名称'].strip():
                        extracted_data.append(stock_data)
                    else:
                        invalid_count += 1
                except Exception as e:
                    self.logger.warning(f"处理股票数据时出错: {e}")
                    invalid_count += 1
            
            if invalid_count > 0:
                self.logger.warning(f"跳过了{invalid_count}条无效数据")
            
            # 最终完成
            success_message = f"数据抓取完成！通过滚动加载获取了{final_row_count}行数据，成功解析{len(extracted_data)}条有效股票数据"
            self.logger.info(success_message)
            
            if progress_callback:
                progress_callback(0, len(extracted_data), success_message)
            
            return extracted_data
            
        except Exception as e:
            error_msg = f"数据抓取失败: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(0, 0, f"抓取失败: {str(e)}")
            return []
    
    def filter_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """筛选数据：只保留有分值的股票"""
        if not self.config['filter_empty_values']:
            return data
        
        filtered_data = []
        target_fields = self.config['target_fields']
        
        for stock in data:
            # 检查是否有任意一个目标字段有值
            has_value = False
            for field in target_fields:
                if field in stock and stock[field] and str(stock[field]).strip():
                    has_value = True
                    break
            
            if has_value:
                filtered_data.append(stock)
        
        self.logger.info(f"筛选后保留 {len(filtered_data)} 条有效数据")
        return filtered_data
    
    def save_to_excel(self, data: List[Dict[str, Any]]) -> str:
        """保存数据到Excel文件"""
        try:
            if not data:
                self.logger.warning("没有数据需要保存")
                return ""
            
            # 创建输出目录
            output_dir = self.config['output_directory']
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.config['output_file_format'].format(timestamp=timestamp)
            filepath = os.path.join(output_dir, filename)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 确保列顺序
            target_fields = self.config['target_fields']
            columns = ['股票代码', '股票名称'] + target_fields
            
            # 重新排列列顺序
            df = df.reindex(columns=columns, fill_value='')
            
            # 保存到Excel
            df.to_excel(filepath, index=False, sheet_name='小草选股数据')
            
            self.logger.info(f"数据已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存Excel文件失败: {str(e)}")
            return ""
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            
            if self.context:
                await self.context.close()
            
            if self.browser:
                await self.browser.close()
            
            if self.playwright:
                await self.playwright.stop()
            
            self.logger.info("浏览器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭浏览器时出错: {str(e)}")
    
    async def run_full_process(self, username: str = "", password: str = ""):
        """运行完整的自动化流程"""
        try:
            # 初始化浏览器
            if not await self.initialize_browser():
                return False, "浏览器初始化失败"
            
            # 导航到网站
            if not await self.navigate_to_site():
                return False, "导航到网站失败"
            
            # 自动登录（如果提供了用户名密码）
            if username and password:
                if not await self.auto_login(username, password):
                    return False, "自动登录失败"
            
            return True, "初始化完成，等待用户手动导航到数据页面"
            
        except Exception as e:
            error_msg = f"自动化流程执行失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    async def extract_and_save(self, progress_callback=None):
        """
        抓取数据并保存
        
        Args:
            progress_callback: 进度回调函数，用于GUI更新
        """
        try:
            # 抓取数据（包含滚动加载功能）
            raw_data = await self.extract_stock_data(progress_callback)
            
            if not raw_data:
                return False, "未抓取到数据", None
            
            # 更新进度：数据筛选
            if progress_callback:
                progress_callback(0, len(raw_data), "正在筛选数据...")
            
            # 筛选数据
            filtered_data = self.filter_data(raw_data)
            
            if not filtered_data:
                return False, "筛选后没有有效数据", None
            
            # 更新进度：保存文件
            if progress_callback:
                progress_callback(0, len(filtered_data), "正在保存Excel文件...")
            
            # 保存到Excel
            filepath = self.save_to_excel(filtered_data)
            
            if not filepath:
                return False, "保存文件失败", None
            
            # 创建详细结果信息
            result_info = {
                'total_extracted': len(raw_data),
                'filtered_count': len(filtered_data),
                'output_file': filepath,
                'data_preview': filtered_data[:5] if len(filtered_data) > 5 else filtered_data,  # 显示前5条数据
                'has_data_fields': self._analyze_data_fields(filtered_data)
            }
            
            success_message = f"成功保存 {len(filtered_data)} 条数据到: {filepath}"
            
            # 最终进度更新
            if progress_callback:
                progress_callback(0, len(filtered_data), success_message)
            
            return True, success_message, result_info
            
        except Exception as e:
            error_msg = f"数据抓取和保存失败: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(0, 0, f"抓取失败: {str(e)}")
            return False, error_msg, None
    
    def _analyze_data_fields(self, data):
        """分析数据字段统计信息"""
        if not data:
            return {}
        
        target_fields = ['小草竞王', '小草红盘起爆', '小草绿盘低吸', '小草连板接力']
        field_stats = {}
        
        for field in target_fields:
            non_empty_count = sum(1 for stock in data if stock.get(field, '').strip() and stock.get(field, '').strip() != '0.00')
            field_stats[field] = {
                'count': non_empty_count,
                'percentage': round(non_empty_count / len(data) * 100, 1) if data else 0
            }
        
        return field_stats