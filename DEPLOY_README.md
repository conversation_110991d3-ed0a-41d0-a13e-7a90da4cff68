# 股票筛选器便携式部署说明

## 项目概述

股票筛选器是一款基于指南针金融软件的自动化股票数据提取和筛选工具。该工具使用OCR技术自动识别和提取股票资金流数据，并根据预设条件进行筛选分析。

本部署包提供便携式安装方案，支持一键部署、智能更新和配置保护功能。

## 系统要求

### 必需软件环境
- **操作系统**: Windows 7/8/10/11 (64位推荐)
- **Python**: 3.7 或更高版本
- **指南针软件**: 已安装并可正常运行的指南针金融软件

### 硬件要求
- **内存**: 至少 4GB RAM (推荐 8GB)
- **存储**: 至少 2GB 可用空间
- **显示器**: 分辨率至少 1024x768
- **GPU**: 可选，支持CUDA的显卡可提升OCR性能

### 网络要求
- 首次部署时需要网络连接以下载Python依赖包
- 后续使用可离线运行

## 部署流程

### 步骤1: 获取部署包

1. 下载部署包压缩文件 `stock_screener_deploy_vX.X.X.XXXX.zip`
2. 解压到目标目录（建议路径不包含中文和空格）
3. 解压后的目录结构应该如下：

```
stock_screener_deploy_vX.X.X.XXXX/
├── src/                    # 程序源码目录
├── config_backup/          # 配置备份目录
├── deploy.bat             # 一键部署脚本
├── start.bat              # 程序启动脚本
├── install_dependencies.py # 依赖安装器
├── update.py              # 更新脚本
├── requirements.txt       # 依赖列表
├── version.txt            # 版本信息
├── version.json           # 版本信息(JSON格式)
└── DEPLOY_README.md       # 本说明文档
```

### 步骤2: 运行一键部署

1. **以管理员权限运行** `deploy.bat` 文件
   - 右键点击 `deploy.bat`
   - 选择"以管理员身份运行"

2. 部署脚本将自动执行以下操作：
   - 检查Python环境
   - 安装所需依赖包
   - 创建启动脚本
   - 创建桌面快捷方式
   - 验证程序安装

3. 等待部署完成，看到"部署完成！"提示

### 步骤3: 首次配置

1. 启动指南针软件并登录
2. 运行股票筛选器：
   - 双击桌面快捷方式"股票筛选器"
   - 或运行 `start.bat`
   - 或执行 `python src/main.py`

3. 首次运行需要配置OCR识别区域：
   - 点击"选择OCR区域"按钮
   - 在指南针软件中框选资金流数据显示区域
   - 确认并保存区域设置

4. 测试OCR识别效果并调整参数

## 使用说明

### 基本操作流程

1. **启动准备**
   - 确保指南针软件已启动并登录
   - 运行股票筛选器

2. **数据提取**
   - 选择或导入股票代码Excel文件
   - 设置筛选条件
   - 点击"开始提取数据"

3. **结果查看**
   - 查看提取的资金流数据
   - 根据筛选结果进行分析
   - 导出筛选结果

### 主要功能

- **自动数据提取**: 使用OCR技术自动识别指南针软件中的资金流数据
- **多天数据获取**: 支持通过键盘导航获取历史数据
- **智能筛选**: 根据预设条件自动筛选符合要求的股票
- **数据导出**: 支持将结果导出为Excel格式
- **配置管理**: 支持保存和管理不同的筛选配置

## 更新流程

### 方式1: 完整更新包

1. 获取新版本的完整部署包
2. 解压新版本到临时目录
3. 运行更新命令：
   ```bash
   python update.py "新版本目录路径"
   ```
4. 按提示完成更新流程

### 方式2: 增量更新包

1. 获取增量更新包（如果提供）
2. 运行更新命令：
   ```bash
   python update.py "更新包路径.zip"
   ```
3. 更新脚本将自动：
   - 备份当前版本
   - 保护用户配置
   - 应用更新文件
   - 更新依赖包
   - 恢复用户配置

### 更新特性

- **版本检查**: 自动比较版本，防止误降级
- **配置保护**: 自动备份和恢复用户自定义配置
- **增量更新**: 只更新变更的文件，节省时间
- **自动回滚**: 更新失败时自动恢复到原版本
- **依赖管理**: 自动处理新增或变更的依赖包

## 故障排除

### 常见问题及解决方案

#### 1. 部署失败：找不到Python环境

**症状**: 部署时提示"未找到Python环境"

**解决方案**:
- 从 [https://www.python.org/downloads/](https://www.python.org/downloads/) 下载并安装Python 3.7+
- 安装时勾选"Add Python to PATH"选项
- 重新运行部署脚本

#### 2. 依赖包安装失败

**症状**: 安装依赖时出现网络错误或包安装失败

**解决方案**:
- 检查网络连接
- 使用国内镜像源：
  ```bash
  pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
  ```
- 手动安装失败的包：
  ```bash
  pip install 包名
  ```

#### 3. OCR识别效果差

**症状**: OCR无法正确识别数字或识别结果不准确

**解决方案**:
- 重新选择OCR区域，确保区域包含完整的数字
- 调整指南针软件的显示比例和字体大小
- 检查显示器分辨率和缩放设置
- 在config.py中调整OCR参数

#### 4. 程序启动失败

**症状**: 双击快捷方式或运行start.bat后程序无法启动

**解决方案**:
- 检查Python环境是否正确安装
- 运行 `python install_dependencies.py` 重新安装依赖
- 查看错误信息，根据提示解决具体问题
- 检查程序文件完整性

#### 5. 指南针软件无法识别

**症状**: 程序提示找不到指南针软件窗口

**解决方案**:
- 确保指南针软件已启动并完全加载
- 检查指南针软件的窗口标题是否包含"指南针"
- 在config.py中调整窗口检测参数
- 尝试以管理员权限运行程序

### 日志文件说明

- **installation_log.json**: 依赖安装日志
- **update_log.json**: 更新操作日志
- **程序运行日志**: 查看控制台输出或日志文件

### 性能优化建议

1. **使用GPU加速**: 如有NVIDIA显卡，安装CUDA以提升OCR性能
2. **调整图像处理参数**: 在config.py中优化图像预处理设置
3. **关闭不必要程序**: 运行时关闭其他占用资源的程序
4. **定期清理**: 清理debug_images目录中的调试图片

## 高级配置

### 配置文件说明

主要配置文件位于 `src/config.py`，包含以下主要配置项：

#### OCR设置
```python
'ocr_settings': {
    'engine_mode': 'simple_paddleocr',  # OCR引擎模式
    'use_gpu': False,                   # 是否使用GPU
    'debug_mode': True,                 # 调试模式
    'confidence_threshold': 0.5         # 置信度阈值
}
```

#### 指南针软件配置
```python
'ocr_region': {
    'x': 466,      # OCR区域X坐标
    'y': 1117,     # OCR区域Y坐标
    'width': 104,  # OCR区域宽度
    'height': 53   # OCR区域高度
}
```

#### 筛选条件配置
```python
FILTER_CONFIG = {
    # 在此配置筛选条件
}
```

### 自定义扩展

1. **自定义OCR策略**: 修改ocr_engines.py添加新的OCR引擎
2. **自定义筛选逻辑**: 修改data_processor.py添加新的筛选条件
3. **自定义界面**: 修改gui_*.py文件自定义用户界面

## 技术支持

### 获取帮助

1. **文档资源**:
   - 查看项目中的CLAUDE.md文件
   - 阅读源码注释和文档字符串

2. **日志分析**:
   - 查看installation_log.json了解安装过程
   - 查看update_log.json了解更新历史
   - 开启debug_mode查看详细运行日志

3. **问题反馈**:
   - 收集错误信息和日志文件
   - 描述操作步骤和环境信息
   - 提供屏幕截图（如有必要）

### 版本信息

- 当前部署包版本：请查看version.txt文件
- Python要求：3.7+
- 主要依赖：PaddleOCR, OpenCV, Pandas, Tkinter

### 更新历史

更新历史记录保存在update_log.json文件中，包含：
- 版本变更记录
- 更新时间和内容
- 文件变更详情

---

**注意**: 本工具仅用于辅助股票分析，不构成投资建议。使用前请确保遵守相关法律法规和软件使用协议。