# -*- coding: utf-8 -*-
"""
网页自动化模块
基于Playwright的topxlc.com小草选股数据抓取
"""

import asyncio
import logging
import os
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON>er, BrowserContext

from config import WEB_AUTOMATION_CONFIG
from optimized_virtual_scroll import OptimizedVirtualScrollHandler
from html_data_parser import create_html_data_parser_manager


class WebAutomator:
    """网页自动化操作类"""
    
    def __init__(self):
        """初始化网页自动化操作器"""
        self.config = WEB_AUTOMATION_CONFIG
        self.logger = logging.getLogger(__name__)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.is_logged_in = False
        self.is_ready_for_extraction = False
        
    async def initialize_browser(self):
        """初始化浏览器"""
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=self.config['browser_headless'],
                timeout=self.config['browser_timeout']
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            
            # 创建新页面
            self.page = await self.context.new_page()
            
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {str(e)}")
            return False
    
    async def navigate_to_site(self):
        """导航到目标网站"""
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            # 导航到目标网站
            await self.page.goto(self.config['target_url'])
            
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(self.config['page_load_wait'])
            
            self.logger.info(f"成功导航到: {self.config['target_url']}")
            return True
            
        except Exception as e:
            self.logger.error(f"导航到网站失败: {str(e)}")
            return False
    
    async def auto_login(self, username: str, password: str):
        """自动登录功能"""
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            # 加载选择器配置
            from web_element_selector import get_web_element_selector_manager
            selector_manager = get_web_element_selector_manager()
            selectors = selector_manager.load_selectors_from_config()
            
            if not selectors:
                self.logger.warning("未找到选择器配置，跳过自动登录")
                return True  # 返回True让用户手动登录
            
            self.logger.info("开始自动登录...")
            
            # 等待并填写用户名
            if 'username_input' in selectors:
                username_selector = selectors['username_input']
                self.logger.info(f"查找用户名输入框: {username_selector}")
                
                await self.page.wait_for_selector(username_selector, timeout=10000)
                await self.page.fill(username_selector, username)
                self.logger.info("用户名已填写")
                
                # 等待一下避免操作过快
                await asyncio.sleep(0.5)
            
            # 等待并填写密码
            if 'password_input' in selectors:
                password_selector = selectors['password_input']
                self.logger.info(f"查找密码输入框: {password_selector}")
                
                await self.page.wait_for_selector(password_selector, timeout=10000)
                await self.page.fill(password_selector, password)
                self.logger.info("密码已填写")
                
                # 等待一下避免操作过快
                await asyncio.sleep(0.5)
            
            # 用户名和密码填写完成，提示用户手动点击登录
            self.logger.info("✅ 用户名和密码填写完成，请手动点击登录按钮")
            self.is_logged_in = True  # 标记为已处理登录流程
            return True
                
        except Exception as e:
            self.logger.error(f"自动登录失败: {str(e)}")
            # 自动登录失败不算致命错误，返回True让用户手动登录
            return True
    
    def wait_for_user_navigation(self):
        """等待用户手动导航到指定页面"""
        """
        这个函数在GUI中被调用，提示用户手动导航到数据页面
        然后用户点击"开始抓取"按钮继续
        """
        self.logger.info("等待用户手动导航到数据页面...")
        return True
    
    def set_ready_for_extraction(self):
        """设置准备开始数据抓取状态"""
        self.is_ready_for_extraction = True
        self.logger.info("已设置为准备抓取数据状态")
    
    async def _scroll_and_load_all_data(self, progress_callback=None):
        """
        优化版滚动加载所有数据（使用OptimizedVirtualScrollHandler）
        
        Args:
            progress_callback: 进度回调函数，接收 (scroll_count, current_rows, message) 参数
            
        Returns:
            tuple: (success, message, final_row_count)
        """
        try:
            # 使用优化的虚拟滚动处理器，并保存引用以便后续获取收集的数据
            self.scroll_handler = OptimizedVirtualScrollHandler(self.page, self.logger)
            return await self.scroll_handler.scroll_and_load_all_data(progress_callback)
            
        except Exception as e:
            error_msg = f"优化滚动加载过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, 0
    async def _detect_scroll_container(self):
        """
        检测页面中的滚动容器
        
        Returns:
            dict: 包含滚动容器信息的字典
        """
        try:
            # 尝试多种策略检测滚动容器
            detect_script = """
            () => {
                // 策略1: 查找常见的滚动容器选择器
                const commonSelectors = [
                    '.table-container',
                    '.scroll-container', 
                    '.data-container',
                    '[class*="scroll"]',
                    '[class*="table"]',
                    '[class*="container"]',
                    '.ant-table-body',
                    '.el-table__body-wrapper',
                    '.c-table'
                ];
                
                for (const selector of commonSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const element of elements) {
                        if (element.scrollHeight > element.clientHeight) {
                            return {
                                found: true,
                                selector: selector,
                                script: `document.querySelector('${selector}')`,
                                description: `容器选择器: ${selector}`
                            };
                        }
                    }
                }
                
                // 策略2: 查找包含表格数据的父容器
                const tableRows = document.querySelectorAll('.c-table-row-body');
                if (tableRows.length > 0) {
                    let parent = tableRows[0].parentElement;
                    while (parent && parent !== document.body) {
                        if (parent.scrollHeight > parent.clientHeight) {
                            const classList = Array.from(parent.classList).join('.');
                            return {
                                found: true,
                                selector: '.' + classList,
                                script: `document.querySelector('.${classList}')`,
                                description: `表格父容器: .${classList}`
                            };
                        }
                        parent = parent.parentElement;
                    }
                }
                
                // 策略3: 查找所有可滚动的元素
                const allElements = document.querySelectorAll('*');
                for (const element of allElements) {
                    const style = window.getComputedStyle(element);
                    if ((style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') 
                        && element.scrollHeight > element.clientHeight) {
                        const classList = Array.from(element.classList).join('.');
                        if (classList) {
                            return {
                                found: true,
                                selector: '.' + classList,
                                script: `document.querySelector('.${classList}')`,
                                description: `可滚动元素: .${classList}`
                            };
                        }
                    }
                }
                
                // 没有找到合适的滚动容器
                return {
                    found: false,
                    selector: null,
                    script: 'window',
                    description: '未找到滚动容器，使用window'
                };
            }
            """
            
            result = await self.page.evaluate(detect_script)
            self.logger.info(f"滚动容器检测结果: {result['description']}")
            return result
            
        except Exception as e:
            self.logger.error(f"检测滚动容器失败: {e}")
            return {
                'found': False,
                'selector': None,
                'script': 'window',
                'description': f'检测失败，使用window: {str(e)}'
            }
    
    async def extract_stock_data(self, progress_callback=None):
        """
        抓取股票数据（包含滚动加载）
        
        Args:
            progress_callback: 进度回调函数，用于GUI更新
        """
        try:
            if not self.page:
                raise Exception("浏览器未初始化")
            
            if not self.is_ready_for_extraction:
                raise Exception("尚未准备好开始数据抓取")
            
            # 等待页面加载完成
            try:
                await self.page.wait_for_load_state('networkidle', timeout=300000)
            except Exception as e:
                self.logger.warning(f"等待页面加载超时: {e}, 继续尝试获取数据")
            
            self.logger.info("开始数据抓取流程...")
            
            # 步骤1: 滚动加载所有数据
            self.logger.info("步骤1: 执行滚动加载，获取所有数据...")
            if progress_callback:
                progress_callback(0, 0, "开始滚动加载数据...")
            
            scroll_success, scroll_message, final_row_count = await self._scroll_and_load_all_data(progress_callback)
            
            if not scroll_success:
                raise Exception(f"滚动加载失败: {scroll_message}")
            
            self.logger.info(f"滚动加载完成: {scroll_message}")
            if progress_callback:
                progress_callback(0, final_row_count, f"滚动加载完成，共{final_row_count}条记录")
            
            # 步骤2: 检查是否有滚动过程中收集的数据
            self.logger.info("步骤2: 检查滚动收集的数据...")
            if progress_callback:
                progress_callback(0, final_row_count, "正在检查滚动收集的数据...")
            
            # 尝试从虚拟滚动器获取已收集的数据
            scroll_handler = getattr(self, 'scroll_handler', None)
            collected_data = []
            
            if scroll_handler and hasattr(scroll_handler, 'get_collected_data'):
                collected_data = scroll_handler.get_collected_data()
                self.logger.info(f"从滚动过程中获取到 {len(collected_data)} 条预收集数据")
            
            # 如果没有预收集数据或数据量不足，则使用传统HTML解析方式
            if len(collected_data) < 10:  # 如果收集的数据太少，可能滚动收集有问题
                self.logger.info("预收集数据不足，使用传统HTML解析方式...")
                
                # 步骤2A: 获取页面HTML内容
                self.logger.info("步骤2A: 获取页面HTML内容...")
                if progress_callback:
                    progress_callback(0, final_row_count, "正在获取页面HTML内容...")
                
                try:
                    html_content = await self.page.content()
                    if not html_content or len(html_content.strip()) < 100:
                        raise Exception("获取到的HTML内容为空或过短，请确保页面已正常加载")
                except Exception as e:
                    raise Exception(f"获取页面HTML内容失败: {str(e)}")
                
                # 步骤3A: 创建HTML数据解析器
                self.logger.info("步骤3A: 初始化HTML数据解析器...")
                if progress_callback:
                    progress_callback(0, final_row_count, "正在初始化数据解析器...")
                
                try:
                    html_parser_manager = create_html_data_parser_manager()
                except Exception as e:
                    raise Exception(f"初始化HTML解析器失败: {str(e)}")
                
                # 步骤4A: 验证HTML结构
                self.logger.info("步骤4A: 验证HTML数据结构...")
                if progress_callback:
                    progress_callback(0, final_row_count, "正在验证数据结构...")
                
                validation_result = html_parser_manager.validate_html_structure(html_content)
                
                if not validation_result['is_valid']:
                    error_details = []
                    if not validation_result.get('header_found'):
                        error_details.append("未找到表头行")
                    if not validation_result.get('data_rows_found'):
                        error_details.append("未找到数据行")
                    if validation_result.get('missing_columns'):
                        error_details.append(f"缺少必要的数据列: {', '.join(validation_result['missing_columns'])}")
                    
                    error_msg = f"页面HTML结构验证失败:\n{'\n'.join(error_details)}"
                    error_msg += "\n\n可能的原因:\n1. 页面数据尚未加载完成\n2. 页面结构发生变化\n3. 当前页面不是数据列表页面"
                    raise Exception(error_msg)
                
                self.logger.info(f"HTML结构验证通过 - 找到{validation_result['total_rows']}行数据，{validation_result['total_columns']}列")
                
                # 如果数据行数很少，可能是虚拟滚动未完全加载
                if validation_result['total_rows'] < 10:
                    self.logger.warning(f"检测到数据行数较少({validation_result['total_rows']}行)，可能虚拟滚动未完全加载所有数据")
                
                # 步骤5A: 提取股票数据
                self.logger.info("步骤5A: 解析提取股票数据...")
                if progress_callback:
                    progress_callback(0, validation_result['total_rows'], f"正在解析{validation_result['total_rows']}条股票数据...")
                
                try:
                    raw_stock_data = html_parser_manager.parse_stock_data_from_html(html_content)
                except Exception as e:
                    raise Exception(f"解析股票数据失败: {str(e)}")
                
                if not raw_stock_data:
                    raise Exception("未能从页面中提取到股票数据，请确保:\n1. 页面已加载完成\n2. 包含股票数据表格\n3. 表格中有有效的股票信息")
                
                # 步骤6A: 转换数据格式
                self.logger.info("步骤6A: 转换数据格式...")
                if progress_callback:
                    progress_callback(0, len(raw_stock_data), f"正在转换{len(raw_stock_data)}条数据格式...")
                
                extracted_data = []
                invalid_count = 0
                
                for stock in raw_stock_data:
                    try:
                        stock_data = {
                            '股票代码': stock.get('stock_code', ''),
                            '股票名称': stock.get('stock_name', ''),
                            '小草竞王': stock.get('jingwang', ''),
                            '小草红盘起爆': stock.get('hongpan_qibao', ''),
                            '小草绿盘低吸': stock.get('lvpan_dixi', ''),
                            '小草连板接力': stock.get('lianban_jieli', '')
                        }
                        # 验证必要字段
                        if stock_data['股票代码'].strip() and stock_data['股票名称'].strip():
                            extracted_data.append(stock_data)
                        else:
                            invalid_count += 1
                    except Exception as e:
                        self.logger.warning(f"处理股票数据时出错: {e}")
                        invalid_count += 1
                
                if invalid_count > 0:
                    self.logger.warning(f"跳过了{invalid_count}条无效数据")
                    
            else:
                # 使用滚动过程中收集的数据
                self.logger.info(f"使用滚动过程中收集的 {len(collected_data)} 条数据")
                extracted_data = collected_data
                
                # 步骤3B: 数据验证和清洗
                self.logger.info("步骤3B: 验证和清洗预收集数据...")
                if progress_callback:
                    progress_callback(0, len(collected_data), f"正在验证{len(collected_data)}条预收集数据...")
                
                # 验证数据完整性
                valid_data = []
                invalid_count = 0
                
                for stock_data in collected_data:
                    try:
                        # 验证必要字段
                        if (stock_data.get('股票代码', '').strip() and 
                            stock_data.get('股票名称', '').strip()):
                            valid_data.append(stock_data)
                        else:
                            invalid_count += 1
                    except Exception as e:
                        self.logger.warning(f"验证股票数据时出错: {e}")
                        invalid_count += 1
                
                extracted_data = valid_data
                if invalid_count > 0:
                    self.logger.warning(f"从预收集数据中剔除了{invalid_count}条无效数据")
            
            # 最终完成
            success_message = f"数据抓取完成！实际获取{len(extracted_data)}条有效股票数据"
            if len(collected_data) >= 10:
                success_message += f"（使用滚动预收集数据）"
            else:
                success_message += f"（使用传统HTML解析，通过滚动加载获取了{final_row_count}行DOM数据）"
                
            self.logger.info(success_message)
            
            if progress_callback:
                progress_callback(0, len(extracted_data), success_message)
            
            return extracted_data
            
        except Exception as e:
            error_msg = f"数据抓取失败: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(0, 0, f"抓取失败: {str(e)}")
            return []
    
    
    def _is_valid_value(self, value: str) -> bool:
        """
        检查数据值是否有效
        
        Args:
            value: 要检查的数据值
            
        Returns:
            bool: 如果值有效返回True，否则返回False
        """
        if not value:
            return False
        
        # 转换为字符串并去除空格
        str_value = str(value).strip()
        
        # 检查是否为配置中定义的无效值
        invalid_values = self.config.get('invalid_values', ['--', '0.00', '', ' ', 'null', 'undefined', 'N/A', '-'])
        
        # 检查是否为无效值
        if str_value in invalid_values:
            return False
        
        # 检查是否为空字符串
        if not str_value:
            return False
            
        return True
    
    def filter_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """筛选数据：只保留有分值的股票，排除--等无效数据"""
        if not self.config['filter_empty_values']:
            return data
        
        filtered_data = []
        target_fields = self.config['target_fields']
        invalid_data_count = 0  # 统计被排除的无效数据数量
        
        for stock in data:
            # 检查是否有任意一个目标字段有有效值
            has_valid_value = False
            for field in target_fields:
                if field in stock and self._is_valid_value(stock[field]):
                    has_valid_value = True
                    break
            
            if has_valid_value:
                filtered_data.append(stock)
            else:
                invalid_data_count += 1
        
        # 增强的日志记录
        self.logger.info(f"数据筛选完成：保留 {len(filtered_data)} 条有效数据，排除 {invalid_data_count} 条无效数据（包含'--'等）")
        return filtered_data
    
    def save_to_excel(self, data: List[Dict[str, Any]]) -> str:
        """保存数据到Excel文件"""
        try:
            if not data:
                self.logger.warning("没有数据需要保存")
                return ""
            
            # 创建输出目录
            output_dir = self.config['output_directory']
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = self.config['output_file_format'].format(timestamp=timestamp)
            filepath = os.path.join(output_dir, filename)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 确保列顺序
            target_fields = self.config['target_fields']
            columns = ['股票代码', '股票名称'] + target_fields
            
            # 重新排列列顺序
            df = df.reindex(columns=columns, fill_value='')
            
            # 保存到Excel
            df.to_excel(filepath, index=False, sheet_name='小草选股数据')
            
            self.logger.info(f"数据已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存Excel文件失败: {str(e)}")
            return ""
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            
            if self.context:
                await self.context.close()
            
            if self.browser:
                await self.browser.close()
            
            if self.playwright:
                await self.playwright.stop()
            
            self.logger.info("浏览器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭浏览器时出错: {str(e)}")
    
    async def run_full_process(self, username: str = "", password: str = ""):
        """运行完整的自动化流程"""
        try:
            # 初始化浏览器
            if not await self.initialize_browser():
                return False, "浏览器初始化失败"
            
            # 导航到网站
            if not await self.navigate_to_site():
                return False, "导航到网站失败"
            
            # 自动登录（如果提供了用户名密码）
            if username and password:
                if not await self.auto_login(username, password):
                    return False, "自动登录失败"
            
            return True, "初始化完成，等待用户手动导航到数据页面"
            
        except Exception as e:
            error_msg = f"自动化流程执行失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    async def extract_and_save(self, progress_callback=None):
        """
        抓取数据并保存
        
        Args:
            progress_callback: 进度回调函数，用于GUI更新
        """
        try:
            # 抓取数据（包含滚动加载功能）
            raw_data = await self.extract_stock_data(progress_callback)
            
            if not raw_data:
                return False, "未抓取到数据", None
            
            # 更新进度：数据筛选
            if progress_callback:
                progress_callback(0, len(raw_data), "正在筛选数据...")
            
            # 筛选数据
            filtered_data = self.filter_data(raw_data)
            
            if not filtered_data:
                return False, "筛选后没有有效数据", None
            
            # 更新进度：保存文件
            if progress_callback:
                progress_callback(0, len(filtered_data), "正在保存Excel文件...")
            
            # 保存到Excel
            filepath = self.save_to_excel(filtered_data)
            
            if not filepath:
                return False, "保存文件失败", None
            
            # 创建详细结果信息
            result_info = {
                'total_extracted': len(raw_data),
                'filtered_count': len(filtered_data),
                'output_file': filepath,
                'data_preview': filtered_data[:5] if len(filtered_data) > 5 else filtered_data,  # 显示前5条数据
                'has_data_fields': self._analyze_data_fields(filtered_data)
            }
            
            success_message = f"成功保存 {len(filtered_data)} 条数据到: {filepath}"
            
            # 最终进度更新
            if progress_callback:
                progress_callback(0, len(filtered_data), success_message)
            
            return True, success_message, result_info
            
        except Exception as e:
            error_msg = f"数据抓取和保存失败: {str(e)}"
            self.logger.error(error_msg)
            if progress_callback:
                progress_callback(0, 0, f"抓取失败: {str(e)}")
            return False, error_msg, None
    
    def _analyze_data_fields(self, data):
        """分析数据字段统计信息，排除--等无效数据"""
        if not data:
            return {}
        
        target_fields = ['小草竞王', '小草红盘起爆', '小草绿盘低吸', '小草连板接力']
        field_stats = {}
        
        for field in target_fields:
            # 使用_is_valid_value()方法统计有效数据数量
            valid_count = sum(1 for stock in data if self._is_valid_value(stock.get(field, '')))
            field_stats[field] = {
                'count': valid_count,
                'percentage': round(valid_count / len(data) * 100, 1) if data else 0
            }
        
        return field_stats