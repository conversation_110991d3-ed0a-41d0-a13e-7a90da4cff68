# -*- coding: utf-8 -*-
"""
基础信号监控模块
包含买入和卖出信号监控的通用逻辑
负责管理股票列表、监控循环和信号状态记录
"""

import logging
import time
import threading
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import pandas as pd
import pyautogui
from config import COMPASS_SOFTWARE, APP_CONFIG


class BaseSignalMonitor(ABC):
    """基础信号监控器抽象类"""
    
    def __init__(self, compass_automator, signal_analyzer, config: Dict[str, Any]):
        """
        初始化基础信号监控器
        
        Args:
            compass_automator: 指南针自动化器实例
            signal_analyzer: 信号分析器实例
            config: 监控配置
        """
        self.logger = logging.getLogger(__name__)
        self.compass_automator = compass_automator
        self.signal_analyzer = signal_analyzer
        self.config = config
        
        # 监控状态
        self.is_monitoring = False
        self.stop_requested = False
        self.current_round = 0
        self.monitoring_thread = None
        
        # 自动停止相关
        self.auto_stop_timer = None
        
        # 股票信号状态存储
        self.signal_states = {}  # {stock_code: SignalState}
        
        # 监控统计
        self.monitoring_stats = {
            'start_time': None,
            'total_rounds': 0,
            'total_stocks_processed': 0,
            'total_signal_changes': 0,
            'total_errors': 0,
            'current_stock_count': 0
        }
        
        self.logger.info(f"{self.__class__.__name__} 初始化完成")
    
    def load_stock_codes(self, excel_path: str) -> List[str]:
        """
        从Excel文件读取股票代码列表
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            股票代码列表
        """
        try:
            self.logger.info(f"正在读取Excel文件: {excel_path}")
            
            # 读取Excel文件
            df = pd.read_excel(excel_path)
            
            # 查找包含股票代码的列
            stock_code_column = None
            possible_columns = ['股票代码', '代码', 'Stock Code', 'Code', '证券代码']
            
            for col in possible_columns:
                if col in df.columns:
                    stock_code_column = col
                    break
            
            if stock_code_column is None:
                # 如果没找到明确的列名，尝试使用第一列
                if len(df.columns) > 0:
                    stock_code_column = df.columns[0]
                    self.logger.warning(f"未找到明确的股票代码列，使用第一列: {stock_code_column}")
                else:
                    raise ValueError("Excel文件为空或无有效列")
            
            # 提取股票代码
            stock_codes = df[stock_code_column].astype(str).tolist()
            
            # 清洗股票代码
            cleaned_codes = []
            for code in stock_codes:
                code = str(code).strip()
                # 移除nan、None等无效值
                if code and code.lower() not in ['nan', 'none', '']:
                    # 确保6位数字格式
                    if len(code) == 6 and code.isdigit():
                        cleaned_codes.append(code)
                    elif len(code) < 6 and code.isdigit():
                        # 补齐前导零
                        cleaned_codes.append(code.zfill(6))
                    else:
                        self.logger.warning(f"跳过无效股票代码: {code}")
            
            self.logger.info(f"成功读取 {len(cleaned_codes)} 个股票代码")
            return cleaned_codes
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {str(e)}")
            raise
    
    def start_monitoring(self, stock_codes: List[str], message_queue) -> bool:
        """
        开始监控
        
        Args:
            stock_codes: 股票代码列表
            message_queue: 消息队列用于GUI通信
            
        Returns:
            是否成功启动
        """
        if self.is_monitoring:
            self.logger.warning("监控已在运行中")
            return False
        
        try:
            self.logger.info(f"开始{self.get_signal_type()}信号监控，共 {len(stock_codes)} 只股票")
            
            # 重置状态
            self.is_monitoring = True
            self.stop_requested = False
            self.current_round = 0
            self.signal_states.clear()
            
            # 更新统计信息
            self.monitoring_stats['start_time'] = datetime.now()
            self.monitoring_stats['total_rounds'] = 0
            self.monitoring_stats['total_stocks_processed'] = 0
            self.monitoring_stats['total_signal_changes'] = 0
            self.monitoring_stats['total_errors'] = 0
            self.monitoring_stats['current_stock_count'] = len(stock_codes)
            
            # 设置自动停止定时器
            self._setup_auto_stop_timer()
            
            # 启动监控线程
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_worker,
                args=(stock_codes, message_queue),
                daemon=True
            )
            self.monitoring_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动监控失败: {str(e)}")
            self.is_monitoring = False
            return False
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.logger.info(f"请求停止{self.get_signal_type()}信号监控")
        self.stop_requested = True
        
        # 取消自动停止定时器
        self._cancel_auto_stop_timer()
        
        # 等待监控线程结束
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
        
        self.is_monitoring = False
        self.logger.info(f"{self.get_signal_type()}信号监控已停止")
    
    def _monitoring_worker(self, stock_codes: List[str], message_queue):
        """
        监控工作线程
        
        Args:
            stock_codes: 股票代码列表
            message_queue: 消息队列
        """
        try:
            message_queue.put(("log", f"📊 开始{self.get_signal_type()}信号监控，共 {len(stock_codes)} 只股票"))
            
            while not self.stop_requested:
                self.current_round += 1
                self.monitoring_stats['total_rounds'] = self.current_round
                
                message_queue.put(("log", f"🔄 开始第 {self.current_round} 轮监控"))
                
                # 处理每只股票
                for i, stock_code in enumerate(stock_codes):
                    if self.stop_requested:
                        break
                    
                    try:
                        # 更新进度
                        progress = (i + 1) / len(stock_codes) * 100
                        message_queue.put(("progress", progress))
                        message_queue.put(("log", f"🔍 正在处理股票: {stock_code} ({i+1}/{len(stock_codes)})"))
                        
                        # 处理单个股票
                        success = self._process_single_stock(stock_code, message_queue)
                        
                        if success:
                            self.monitoring_stats['total_stocks_processed'] += 1
                        else:
                            self.monitoring_stats['total_errors'] += 1
                        
                        # 短暂延迟避免操作过快
                        time.sleep(0.5)
                        
                    except Exception as e:
                        self.logger.error(f"处理股票 {stock_code} 时出错: {str(e)}")
                        message_queue.put(("log", f"❌ 股票 {stock_code} 处理出错: {str(e)}，继续下一个"))
                        self.monitoring_stats['total_errors'] += 1
                        continue
                
                if self.stop_requested:
                    break
                
                # 一轮完成
                message_queue.put(("progress", 0))
                message_queue.put(("log", f"✅ 第 {self.current_round} 轮监控完成，等待 {self.config.get('round_interval', 5)} 秒后开始下一轮"))
                
                # 等待下一轮间隔
                for _ in range(int(self.config.get('round_interval', 5) * 10)):
                    if self.stop_requested:
                        break
                    time.sleep(0.1)
            
            message_queue.put(("log", f"🛑 {self.get_signal_type()}信号监控已停止，共完成 {self.current_round} 轮"))
            
        except Exception as e:
            self.logger.error(f"监控线程出错: {str(e)}")
            message_queue.put(("log", f"❌ 监控线程出错: {str(e)}"))
        finally:
            # 清理资源
            self._cancel_auto_stop_timer()
            self.is_monitoring = False
            
            # 发送GUI停止消息（如果是UnifiedSignalMonitor的实例）
            if hasattr(self, 'signal_type'):
                try:
                    message_queue.put((f"{self.signal_type}_signal_monitoring_stopped", None))
                    self.logger.debug(f"已发送{self.get_signal_type()}信号监控停止消息到GUI")
                except Exception as e:
                    self.logger.error(f"发送GUI停止消息失败: {str(e)}")
    
    def _move_mouse_to_target_position(self) -> bool:
        """
        将鼠标移动到配置的基准位置
        
        Returns:
            是否移动成功
        """
        try:
            # 检查是否启用鼠标定位功能
            if not APP_CONFIG.get('enable_mouse_positioning', True):
                self.logger.debug("鼠标定位功能已禁用，跳过鼠标移动")
                return True  # 返回True表示操作成功（虽然被跳过了）
            
            # 获取鼠标基准位置配置
            mouse_config = COMPASS_SOFTWARE.get('mouse_target_position')
            if not mouse_config:
                self.logger.warning("未配置鼠标基准位置")
                return False
            
            x, y = mouse_config['x'], mouse_config['y']
            
            # 检查坐标是否有效（非默认值）
            if x == 0 and y == 0:
                self.logger.warning("鼠标基准位置未设置（坐标为0,0）")
                return False
            
            self.logger.debug(f"移动鼠标到基准位置: ({x}, {y})")
            
            # 移动鼠标
            pyautogui.moveTo(x, y, duration=0.2)
            
            # 验证移动是否成功
            current_pos = pyautogui.position()
            if abs(current_pos.x - x) <= 2 and abs(current_pos.y - y) <= 2:
                return True
            else:
                self.logger.warning(f"鼠标移动可能不准确，目标位置: ({x}, {y})，实际位置: ({current_pos.x}, {current_pos.y})")
                return False
                
        except Exception as e:
            self.logger.error(f"移动鼠标到基准位置失败: {str(e)}")
            return False
    
    @abstractmethod
    def _process_single_stock(self, stock_code: str, message_queue) -> bool:
        """
        处理单个股票的信号识别 - 子类必须实现
        
        Args:
            stock_code: 股票代码
            message_queue: 消息队列
            
        Returns:
            是否处理成功
        """
        pass
    
    @abstractmethod
    def get_signal_type(self) -> str:
        """获取信号类型名称 - 子类必须实现"""
        pass
    
    def _record_signal_state(self, stock_code: str, signal: str) -> Tuple[bool, Dict[str, Any]]:
        """
        记录股票信号状态并检测变化
        
        Args:
            stock_code: 股票代码
            signal: 当前信号
            
        Returns:
            (是否有变化, 变化信息)
        """
        current_time = datetime.now().strftime('%H:%M:%S')
        
        if stock_code not in self.signal_states:
            # 首次记录
            self.signal_states[stock_code] = {
                'first_signal': signal,
                'current_signal': signal,
                'last_check_time': current_time,
                'change_count': 0,
                'history': [
                    {
                        'time': current_time,
                        'from': None,
                        'to': signal,
                        'round': self.current_round
                    }
                ]
            }
            
            return True, {
                'is_first_record': True,
                'old_signal': None,
                'new_signal': signal
            }
        else:
            # 检查变化
            old_signal = self.signal_states[stock_code]['current_signal']
            
            if old_signal != signal:
                # 有变化，更新记录
                self.signal_states[stock_code]['current_signal'] = signal
                self.signal_states[stock_code]['last_check_time'] = current_time
                self.signal_states[stock_code]['change_count'] += 1
                self.signal_states[stock_code]['history'].append({
                    'time': current_time,
                    'from': old_signal,
                    'to': signal,
                    'round': self.current_round
                })
                
                return True, {
                    'is_first_record': False,
                    'old_signal': old_signal,
                    'new_signal': signal
                }
            else:
                # 无变化，仅更新检查时间
                self.signal_states[stock_code]['last_check_time'] = current_time
                
                return False, {
                    'is_first_record': False,
                    'old_signal': old_signal,
                    'new_signal': signal
                }
    
    def get_monitoring_stats(self) -> Dict[str, Any]:
        """
        获取监控统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.monitoring_stats.copy()
        
        # 计算运行时间
        if stats['start_time']:
            elapsed = datetime.now() - stats['start_time']
            stats['elapsed_seconds'] = int(elapsed.total_seconds())
            stats['elapsed_formatted'] = str(elapsed).split('.')[0]  # 移除微秒
        else:
            stats['elapsed_seconds'] = 0
            stats['elapsed_formatted'] = "00:00:00"
        
        # 计算信号变化的股票数量
        stats['stocks_with_changes'] = len([
            code for code, state in self.signal_states.items()
            if state['change_count'] > 0
        ])
        
        return stats
    
    def get_signal_states(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有股票的信号状态
        
        Returns:
            信号状态字典
        """
        return self.signal_states.copy()
    
    def get_stocks_by_signal(self, signal: str) -> List[str]:
        """
        获取具有指定信号的股票列表
        
        Args:
            signal: 信号类型
            
        Returns:
            股票代码列表
        """
        return [
            code for code, state in self.signal_states.items()
            if state['current_signal'] == signal
        ]
    
    def export_signal_history(self, filepath: str) -> bool:
        """
        导出信号变化历史到文件
        
        Args:
            filepath: 输出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            history_data = []
            
            for stock_code, state in self.signal_states.items():
                for change in state['history']:
                    history_data.append({
                        '股票代码': stock_code,
                        '时间': change['time'],
                        '轮次': change['round'],
                        '变化前': change['from'] or '无',
                        '变化后': change['to'],
                        '首次信号': state['first_signal'],
                        '变化次数': state['change_count']
                    })
            
            if history_data:
                df = pd.DataFrame(history_data)
                df.to_excel(filepath, index=False)
                self.logger.info(f"信号历史已导出到: {filepath}")
                return True
            else:
                self.logger.warning("没有信号历史数据可导出")
                return False
                
        except Exception as e:
            self.logger.error(f"导出信号历史失败: {str(e)}")
            return False
    
    def _calculate_seconds_until_stop_time(self) -> int:
        """
        计算到15:01的剩余秒数
        
        Returns:
            剩余秒数，如果已过时间则返回0
        """
        try:
            now = datetime.now()
            today_stop_time = now.replace(hour=15, minute=15, second=0, microsecond=0)
            
            # 如果当前时间已过15:01，返回0（不设置定时器）
            if now >= today_stop_time:
                return 0
            
            # 计算剩余时间
            time_diff = today_stop_time - now
            return int(time_diff.total_seconds())
            
        except Exception as e:
            self.logger.error(f"计算自动停止时间失败: {str(e)}")
            return 0
    
    def _setup_auto_stop_timer(self):
        """
        设置15:01自动停止定时器
        """
        try:
            # 检查是否启用自动停止功能
            if not self.config.get('auto_stop_at_market_close', False):
                self.logger.debug("自动停止功能未启用")
                return
            
            # 计算到15:01的剩余时间
            seconds_until_stop = self._calculate_seconds_until_stop_time()
            
            if seconds_until_stop <= 0:
                self.logger.info("当前时间已过15:01，不设置自动停止定时器")
                return
            
            # 创建并启动定时器
            self.auto_stop_timer = threading.Timer(seconds_until_stop, self._auto_stop_callback)
            self.auto_stop_timer.daemon = True  # 设置为守护线程
            self.auto_stop_timer.start()
            
            # 计算停止时间用于日志显示
            stop_time = datetime.now() + timedelta(seconds=seconds_until_stop)
            self.logger.info(f"已设置15:01自动停止定时器，将在 {stop_time.strftime('%H:%M:%S')} 触发")
            
        except Exception as e:
            self.logger.error(f"设置自动停止定时器失败: {str(e)}")
    
    def _auto_stop_callback(self):
        """
        自动停止定时器回调函数
        """
        try:
            self.logger.info(f"⏰ 到达15:01，自动停止{self.get_signal_type()}信号监控")
            self.stop_requested = True
            
        except Exception as e:
            self.logger.error(f"自动停止回调执行失败: {str(e)}")
    
    def _cancel_auto_stop_timer(self):
        """
        取消自动停止定时器
        """
        try:
            if self.auto_stop_timer and self.auto_stop_timer.is_alive():
                self.auto_stop_timer.cancel()
                self.logger.debug("自动停止定时器已取消")
            self.auto_stop_timer = None
            
        except Exception as e:
            self.logger.error(f"取消自动停止定时器失败: {str(e)}")