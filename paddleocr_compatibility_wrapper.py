# -*- coding: utf-8 -*-
"""
PaddleOCR兼容性包装器
将简化的PaddleOCR引擎包装成与现有系统兼容的接口
保持与原有OCR系统的接口一致性
"""

import logging
import time
from typing import Dict, Any, Optional
from simple_paddleocr_engine import SimplePaddleOCREngine
from config import APP_CONFIG


class PaddleOCRCompatibilityWrapper:
    """PaddleOCR兼容性包装器"""
    
    def __init__(self, use_gpu: bool = False, debug_mode: bool = False):
        """
        初始化兼容性包装器
        
        Args:
            use_gpu: 是否使用GPU
            debug_mode: 是否启用调试模式
        """
        self.logger = logging.getLogger(__name__)
        self.debug_mode = debug_mode
        self.use_gpu = use_gpu
        
        # 初始化简化的PaddleOCR引擎
        self.simple_engine = SimplePaddleOCREngine(use_gpu=use_gpu, debug_mode=debug_mode)
        
        # 统计信息
        self.stats = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'total_execution_time': 0.0
        }
    
    def is_available(self) -> bool:
        """检查引擎是否可用"""
        return self.simple_engine.is_available()
    
    def get_engine_status(self) -> Dict[str, Any]:
        """
        获取引擎状态（兼容原有接口）
        
        Returns:
            引擎状态字典
        """
        return {
            'available_engines': {
                'paddleocr': self.is_available()
            },
            'debug_mode': self.debug_mode,
            'save_debug_images': False,  # 简化版不保存调试图像
            'use_gpu': self.use_gpu,
            'engine_type': 'simple_paddleocr'
        }
    
    def test_fund_data_ocr_recognition_optimized(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        优化版资金数据OCR识别（兼容原有接口）
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域宽高
            
        Returns:
            识别结果字典
        """
        start_time = time.time()
        self.stats['total_calls'] += 1
        
        try:
            if self.debug_mode:
                self.logger.debug(f"开始简化PaddleOCR识别，区域: ({x}, {y}, {width}, {height})")
            
            # 使用简化引擎进行识别
            result = self.simple_engine.capture_and_recognize_region(x, y, width, height)
            
            execution_time = time.time() - start_time
            self.stats['total_execution_time'] += execution_time
            
            if result.get('success'):
                self.stats['successful_calls'] += 1
                
                # 添加兼容性字段
                result.update({
                    'best_strategy': 'simple_paddleocr',
                    'best_confidence': result.get('confidence', 1.0),
                    'optimization_applied': True,
                    'early_exit': True,
                    'execution_time': execution_time,
                    'debug_images_saved': False,
                    'timestamp': time.strftime('%Y%m%d_%H%M%S')
                })
                
                if self.debug_mode:
                    fund_values = result.get('fund_values', [])
                    self.logger.debug(f"简化PaddleOCR识别成功，识别到{len(fund_values)}个数值: {fund_values}")
                    self.logger.debug(f"执行时间: {execution_time:.3f}s")
                
            else:
                self.stats['failed_calls'] += 1
                self.logger.warning(f"简化PaddleOCR识别失败: {result.get('error', '未知错误')}")
            
            return result
            
        except Exception as e:
            self.stats['failed_calls'] += 1
            execution_time = time.time() - start_time
            self.stats['total_execution_time'] += execution_time
            
            self.logger.error(f"简化PaddleOCR识别异常: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }
    
    def test_fund_data_ocr_recognition(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        标准版资金数据OCR识别（兼容原有接口）
        直接调用优化版实现
        """
        return self.test_fund_data_ocr_recognition_optimized(x, y, width, height)
    
    def test_raw_ocr_recognition(self, x: int, y: int, width: int, height: int) -> Dict[str, Any]:
        """
        原始OCR识别测试（兼容原有接口）
        
        Args:
            x, y: 区域左上角坐标
            width, height: 区域宽高
            
        Returns:
            识别结果字典
        """
        try:
            if self.debug_mode:
                self.logger.debug(f"开始原始OCR识别测试，区域: ({x}, {y}, {width}, {height})")
            
            # 截取屏幕区域
            screenshot = self.simple_engine._capture_screen_region(x, y, width, height)
            if screenshot is None:
                return {'success': False, 'error': '截图失败'}
            
            # 识别文本
            texts = self.simple_engine.recognize_image(screenshot)
            
            return {
                'success': True,
                'texts': texts,
                'raw_texts': texts,
                'engine': 'simple_paddleocr'
            }
            
        except Exception as e:
            self.logger.error(f"原始OCR识别测试失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        total_calls = self.stats['total_calls']
        if total_calls > 0:
            success_rate = self.stats['successful_calls'] / total_calls * 100
            avg_execution_time = self.stats['total_execution_time'] / total_calls
        else:
            success_rate = 0.0
            avg_execution_time = 0.0
        
        return {
            'total_calls': total_calls,
            'successful_calls': self.stats['successful_calls'],
            'failed_calls': self.stats['failed_calls'],
            'success_rate': success_rate,
            'total_execution_time': self.stats['total_execution_time'],
            'average_execution_time': avg_execution_time,
            'engine_type': 'simple_paddleocr'
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'total_execution_time': 0.0
        }
        
        if self.debug_mode:
            self.logger.debug("已重置统计信息")


def create_paddleocr_wrapper(use_gpu: bool = None, debug_mode: bool = None) -> PaddleOCRCompatibilityWrapper:
    """
    创建PaddleOCR兼容性包装器
    
    Args:
        use_gpu: 是否使用GPU，None时从配置读取
        debug_mode: 是否启用调试模式，None时从配置读取
        
    Returns:
        PaddleOCR兼容性包装器实例
    """
    if use_gpu is None:
        use_gpu = APP_CONFIG.get('ocr_use_gpu', False)
    
    if debug_mode is None:
        debug_mode = APP_CONFIG.get('debug_mode', False)
    
    return PaddleOCRCompatibilityWrapper(use_gpu=use_gpu, debug_mode=debug_mode)
