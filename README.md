# 股票筛选器 - 指南针数据分析工具

基于指南针全赢数据分析系统的自动化股票筛选工具，用于提取股票多空资金数据并进行筛选分析。

## 功能特点

- 🔍 **自动化数据提取**: 通过GUI自动化技术从指南针软件获取股票数据
- 📊 **Excel数据处理**: 支持从Excel文件批量读取股票代码
- 🎯 **智能筛选**: 基于多空资金数据的递增趋势进行股票筛选
- 🖥️ **图形界面**: 直观的GUI界面，操作简单便捷
- 📈 **数据导出**: 筛选结果可导出为Excel文件

## 系统要求

- **操作系统**: Windows 10/11 (由于需要与指南针软件交互)
- **Python版本**: 3.8+
- **指南针软件**: 需要安装指南针全赢数据分析系统
- **内存**: 建议4GB以上 (OCR模式需要更多内存)

## 安装步骤

### 1. 克隆或下载项目

```bash
git clone [repository-url]
cd stock_screener
```

### 2. 安装Python依赖

```bash
pip install -r requirements.txt
```

**注意**: 首次安装EasyOCR时会自动下载OCR模型，需要稳定的网络连接。

### 3. 配置指南针软件路径

编辑 `config.py` 文件，修改以下配置：

```python
COMPASS_SOFTWARE = {
    'exe_path': r'C:\Program Files\Compass\Compass.exe',  # 修改为实际路径
    'process_name': 'Compass.exe',
    'main_window_title': '指南针全赢数据分析系统',
    # ... 其他配置
}
```

### 4. 控件配置 (可选)

如果默认控件识别不准确，可以修改 `config.py` 中的控件配置：

```python
'search_box': {
    'auto_id': 'SearchBox',      # 根据实际情况调整
    'control_type': 'Edit',
    'class_name': 'Edit'
},
'fund_flow_area': {
    'today': {
        'auto_id': 'TodayFundFlow',    # 根据实际情况调整
        'control_type': 'Text',
        'class_name': 'Static'
    },
    # ... 其他控件配置
}
```

## 使用方法

### 1. 启动程序

```bash
python main.py
```

### 2. 准备Excel文件

创建包含股票代码的Excel文件，股票代码应位于第一列。支持格式：
- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

示例Excel文件格式：
```
股票代码
000001
000002
000858
600000
600036
```

### 3. 操作步骤

1. **选择Excel文件**: 点击"选择文件"按钮，选择包含股票代码的Excel文件
2. **测试连接**: 点击"测试连接"按钮，确保程序能够连接到指南针软件
3. **开始分析**: 点击"开始分析"按钮，程序将自动：
   - 读取Excel文件中的股票代码
   - 启动指南针软件
   - 逐个搜索股票并获取多空资金数据
   - 根据筛选条件筛选符合条件的股票
4. **查看结果**: 在结果表格中查看筛选结果
5. **保存结果**: 点击"保存结果"按钮，将筛选结果导出为Excel文件

### 4. 筛选条件

默认筛选条件：**今日资金 > 昨日资金 > 前日资金**

可在 `config.py` 中修改筛选逻辑：

```python
FILTER_CONFIG = {
    'filter_logic': 'today > yesterday > day_before_yesterday',
    'validate_data': True,
    'data_range': {
        'min_value': -999999,
        'max_value': 999999
    }
}
```

## 配置说明

### 应用配置 (APP_CONFIG)

```python
APP_CONFIG = {
    'use_ocr_mode': False,        # 是否使用OCR模式
    'wait_time': 2,               # 操作等待时间
    'page_load_wait': 3,          # 页面加载等待时间
    'max_retries': 3,             # 最大重试次数
    'log_level': 'INFO'           # 日志级别
}
```

### OCR配置 (OCR_CONFIG)

```python
OCR_CONFIG = {
    'languages': ['ch_sim', 'en'], # 支持的语言
    'confidence_threshold': 0.7,   # 置信度阈值
    'use_gpu': False               # 是否使用GPU
}
```

## 故障排除

### 常见问题

1. **无法连接到指南针软件**
   - 确保指南针软件已正确安装且可正常运行
   - 检查 `config.py` 中的软件路径是否正确
   - 确保指南针软件的窗口标题与配置一致

2. **控件识别失败**
   - 使用Windows自带的"检查"工具查看控件属性
   - 修改 `config.py` 中的控件配置
   - 考虑使用OCR模式作为备选方案

3. **OCR识别不准确**
   - 调整OCR区域坐标 (`ocr_region`)
   - 修改置信度阈值 (`confidence_threshold`)
   - 如果有GPU，可以启用GPU加速

4. **Excel文件读取失败**
   - 检查Excel文件格式是否支持
   - 确保股票代码位于第一列
   - 检查文件是否被其他程序占用

### 调试模式

启用调试模式可以获取更详细的日志信息：

```python
APP_CONFIG = {
    'log_level': 'DEBUG'
}
```

## 注意事项

1. **合规使用**: 请确保符合相关法律法规和指南针软件的使用协议
2. **数据准确性**: 程序仅作为辅助工具，投资决策请以官方数据为准
3. **系统兼容性**: 程序主要针对Windows系统设计，在其他系统上可能需要调整
4. **性能影响**: 自动化操作可能会影响指南针软件的性能

## 许可证

本项目仅供学习和研究使用。

## 更新日志

### v1.0.0 (当前版本)
- 基本的GUI界面
- 支持Excel文件读取
- 指南针软件自动化
- 数据筛选功能
- 结果导出功能

## 技术支持

如遇到问题，请检查：
1. 配置文件是否正确
2. 依赖包是否完整安装
3. 指南针软件是否正常运行
4. 查看程序日志获取详细错误信息