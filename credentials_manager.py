# -*- coding: utf-8 -*-
"""
网页登录凭据管理器
提供用户名密码的本地保存和加载功能
"""

import json
import base64
import os
import logging
from typing import Tuple, Optional


class CredentialsManager:
    """网页登录凭据管理器"""
    
    def __init__(self, config_file: str = "web_credentials.json"):
        """
        初始化凭据管理器
        
        Args:
            config_file: 凭据保存文件路径
        """
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
    
    def save_credentials(self, username: str, password: str) -> bool:
        """
        保存用户名和密码到本地文件
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if not username.strip():
                self.logger.warning("用户名为空，不保存凭据")
                return False
            
            # 使用base64编码（注意：这不是加密，只是简单编码避免明文显示）
            credentials_str = f"{username}:{password}"
            encoded_data = base64.b64encode(credentials_str.encode('utf-8')).decode('utf-8')
            
            # 保存到JSON文件
            data = {
                'encoded_credentials': encoded_data,
                'username_hint': username[:2] + '*' * (len(username) - 2),  # 用户名提示
                'saved_at': self._get_current_time()
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"登录凭据已保存到 {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存登录凭据失败: {str(e)}")
            return False
    
    def load_credentials(self) -> Tuple[str, str]:
        """
        从本地文件加载用户名和密码
        
        Returns:
            Tuple[str, str]: (用户名, 密码)，如果没有保存的凭据则返回空字符串
        """
        try:
            if not os.path.exists(self.config_file):
                self.logger.debug("凭据文件不存在")
                return "", ""
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            encoded_data = data.get('encoded_credentials', '')
            if not encoded_data:
                self.logger.debug("凭据文件中没有有效数据")
                return "", ""
            
            # 解码凭据
            decoded_str = base64.b64decode(encoded_data.encode('utf-8')).decode('utf-8')
            
            # 分割用户名和密码
            if ':' in decoded_str:
                username, password = decoded_str.split(':', 1)
                self.logger.info(f"成功加载保存的登录凭据，用户名: {data.get('username_hint', 'N/A')}")
                return username, password
            else:
                self.logger.warning("凭据格式错误")
                return "", ""
                
        except Exception as e:
            self.logger.error(f"加载登录凭据失败: {str(e)}")
            return "", ""
    
    def has_saved_credentials(self) -> bool:
        """
        检查是否有保存的凭据
        
        Returns:
            bool: 是否有保存的凭据
        """
        username, password = self.load_credentials()
        return bool(username.strip() and password.strip())
    
    def get_credentials_info(self) -> Optional[dict]:
        """
        获取保存的凭据信息（不包含实际密码）
        
        Returns:
            dict: 凭据信息，包含用户名提示和保存时间
        """
        try:
            if not os.path.exists(self.config_file):
                return None
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return {
                'username_hint': data.get('username_hint', 'N/A'),
                'saved_at': data.get('saved_at', 'N/A'),
                'has_credentials': bool(data.get('encoded_credentials'))
            }
            
        except Exception as e:
            self.logger.error(f"获取凭据信息失败: {str(e)}")
            return None
    
    def clear_credentials(self) -> bool:
        """
        清除保存的凭据
        
        Returns:
            bool: 清除是否成功
        """
        try:
            if os.path.exists(self.config_file):
                os.remove(self.config_file)
                self.logger.info("已清除保存的登录凭据")
                return True
            else:
                self.logger.info("没有需要清除的凭据文件")
                return True
                
        except Exception as e:
            self.logger.error(f"清除登录凭据失败: {str(e)}")
            return False
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


# 全局凭据管理器实例
_credentials_manager = None

def get_credentials_manager() -> CredentialsManager:
    """
    获取全局凭据管理器实例
    
    Returns:
        CredentialsManager: 凭据管理器实例
    """
    global _credentials_manager
    if _credentials_manager is None:
        _credentials_manager = CredentialsManager()
    return _credentials_manager


if __name__ == "__main__":
    # 测试代码
    import sys
    logging.basicConfig(level=logging.INFO)
    
    cm = CredentialsManager("test_credentials.json")
    
    # 测试保存和加载
    print("测试保存凭据...")
    success = cm.save_credentials("testuser", "testpass123")
    print(f"保存结果: {success}")
    
    print("\n测试加载凭据...")
    username, password = cm.load_credentials()
    print(f"加载结果: 用户名='{username}', 密码='{password}'")
    
    print("\n测试凭据信息...")
    info = cm.get_credentials_info()
    print(f"凭据信息: {info}")
    
    print("\n测试清除凭据...")
    success = cm.clear_credentials()
    print(f"清除结果: {success}")
    
    # 清理测试文件
    try:
        os.remove("test_credentials.json")
    except:
        pass