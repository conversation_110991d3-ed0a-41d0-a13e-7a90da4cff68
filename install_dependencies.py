# -*- coding: utf-8 -*-
"""
股票筛选器智能依赖安装器
自动检测环境并安装所需依赖包，支持增量安装和错误重试
"""

import sys
import subprocess
import pkg_resources
import platform
import os
import time
from pathlib import Path
import json
import importlib.util

class DependencyInstaller:
    def __init__(self, requirements_file="requirements.txt"):
        """
        初始化依赖安装器
        
        Args:
            requirements_file: requirements.txt文件路径
        """
        self.requirements_file = Path(requirements_file)
        self.python_executable = sys.executable
        self.platform_info = self.get_platform_info()
        self.installation_log = []
        
        print("=" * 60)
        print("股票筛选器智能依赖安装器")
        print("=" * 60)
        print(f"Python版本: {sys.version}")
        print(f"Python路径: {self.python_executable}")
        print(f"操作系统: {self.platform_info['system']} {self.platform_info['version']}")
        print(f"架构: {self.platform_info['machine']}")
        print("=" * 60)
    
    def get_platform_info(self):
        """获取平台信息"""
        return {
            'system': platform.system(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
        }
    
    def check_python_version(self):
        """检查Python版本是否符合要求"""
        version = sys.version_info
        
        if version.major < 3:
            print("❌ 错误: 需要Python 3.x版本，当前版本为Python 2.x")
            return False
        
        if version.major == 3 and version.minor < 7:
            print(f"⚠️  警告: Python {version.major}.{version.minor} 可能不支持所有功能")
            print("   推荐使用Python 3.7或更高版本")
            
            response = input("是否继续安装？(y/n): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                return False
        
        print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def get_installed_packages(self):
        """获取已安装的包列表"""
        try:
            installed_packages = {}
            for dist in pkg_resources.working_set:
                installed_packages[dist.project_name.lower()] = dist.version
            return installed_packages
        except Exception as e:
            print(f"⚠️  获取已安装包列表失败: {e}")
            return {}
    
    def parse_requirements(self):
        """解析requirements.txt文件"""
        if not self.requirements_file.exists():
            print(f"❌ 错误: 找不到requirements文件: {self.requirements_file}")
            return []
        
        requirements = []
        try:
            with open(self.requirements_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    # 跳过注释和空行
                    if line and not line.startswith('#'):
                        requirements.append(line)
            
            print(f"📋 从{self.requirements_file}解析到{len(requirements)}个依赖包")
            return requirements
        except Exception as e:
            print(f"❌ 解析requirements文件失败: {e}")
            return []
    
    def check_package_installed(self, package_spec):
        """检查单个包是否已安装"""
        # 从包规格中提取包名（去掉版本要求）
        package_name = package_spec.split('>=')[0].split('==')[0].split('<')[0].split('>')[0].strip()
        
        try:
            # 尝试导入包
            spec = importlib.util.find_spec(package_name)
            if spec is not None:
                return True
            
            # 对于一些特殊包名的映射
            package_mappings = {
                'opencv-python': 'cv2',
                'pillow': 'PIL',
                'scikit-learn': 'sklearn',
            }
            
            mapped_name = package_mappings.get(package_name.lower())
            if mapped_name:
                spec = importlib.util.find_spec(mapped_name)
                return spec is not None
            
            return False
        except Exception:
            return False
    
    def install_package(self, package_spec, max_retries=3):
        """安装单个包，支持重试"""
        package_name = package_spec.split('>=')[0].split('==')[0].split('<')[0].split('>')[0].strip()
        
        for attempt in range(max_retries):
            try:
                print(f"📦 正在安装 {package_name}... (尝试 {attempt + 1}/{max_retries})")
                
                # 构建pip安装命令
                cmd = [self.python_executable, '-m', 'pip', 'install', package_spec]
                
                # 对于一些大包，使用特殊的安装选项
                if package_name.lower() in ['torch', 'torchvision', 'paddlepaddle', 'paddleocr']:
                    cmd.extend(['--timeout', '300'])  # 增加超时时间
                
                # 执行安装
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=600  # 10分钟超时
                )
                
                if result.returncode == 0:
                    print(f"✅ {package_name} 安装成功")
                    self.installation_log.append({
                        'package': package_name,
                        'status': 'success',
                        'attempt': attempt + 1
                    })
                    return True
                else:
                    error_msg = result.stderr.strip()
                    print(f"❌ {package_name} 安装失败 (尝试 {attempt + 1}): {error_msg}")
                    
                    if attempt < max_retries - 1:
                        print(f"⏳ 等待5秒后重试...")
                        time.sleep(5)
            
            except subprocess.TimeoutExpired:
                print(f"⏰ {package_name} 安装超时 (尝试 {attempt + 1})")
                if attempt < max_retries - 1:
                    print(f"⏳ 等待10秒后重试...")
                    time.sleep(10)
            
            except Exception as e:
                print(f"❌ {package_name} 安装出错 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    print(f"⏳ 等待5秒后重试...")
                    time.sleep(5)
        
        print(f"💥 {package_name} 安装失败，已达到最大重试次数")
        self.installation_log.append({
            'package': package_name,
            'status': 'failed',
            'attempts': max_retries
        })
        return False
    
    def upgrade_pip(self):
        """升级pip到最新版本"""
        print("🔄 正在升级pip...")
        try:
            cmd = [self.python_executable, '-m', 'pip', 'install', '--upgrade', 'pip']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("✅ pip升级成功")
                return True
            else:
                print(f"⚠️  pip升级失败: {result.stderr.strip()}")
                return False
        except Exception as e:
            print(f"⚠️  pip升级出错: {e}")
            return False
    
    def detect_gpu_support(self):
        """检测GPU支持情况"""
        gpu_info = {
            'cuda_available': False,
            'gpu_devices': [],
            'recommendation': 'cpu'
        }
        
        try:
            # 尝试检测NVIDIA GPU
            nvidia_result = subprocess.run(
                ['nvidia-smi', '--query-gpu=name', '--format=csv,noheader'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if nvidia_result.returncode == 0:
                gpu_devices = [name.strip() for name in nvidia_result.stdout.strip().split('\n') if name.strip()]
                gpu_info['cuda_available'] = True
                gpu_info['gpu_devices'] = gpu_devices
                gpu_info['recommendation'] = 'gpu'
                
                print("🎮 检测到NVIDIA GPU:")
                for i, device in enumerate(gpu_devices):
                    print(f"   GPU {i}: {device}")
        
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        if not gpu_info['cuda_available']:
            print("💻 未检测到CUDA支持，将使用CPU模式")
        
        return gpu_info
    
    def install_dependencies(self):
        """安装所有依赖"""
        # 1. 检查Python版本
        if not self.check_python_version():
            return False
        
        # 2. 升级pip
        self.upgrade_pip()
        
        # 3. 检测GPU支持
        gpu_info = self.detect_gpu_support()
        
        # 4. 解析requirements
        requirements = self.parse_requirements()
        if not requirements:
            print("❌ 没有找到有效的依赖包配置")
            return False
        
        # 5. 检查已安装的包
        print("🔍 检查已安装的包...")
        installed_packages = self.get_installed_packages()
        
        packages_to_install = []
        packages_already_installed = []
        
        for req in requirements:
            if self.check_package_installed(req):
                packages_already_installed.append(req)
            else:
                packages_to_install.append(req)
        
        # 6. 显示安装计划
        print(f"\n📊 安装计划:")
        print(f"   ✅ 已安装: {len(packages_already_installed)} 个包")
        print(f"   📦 需要安装: {len(packages_to_install)} 个包")
        
        if packages_already_installed:
            print(f"\n已安装的包:")
            for pkg in packages_already_installed:
                print(f"   ✅ {pkg}")
        
        if packages_to_install:
            print(f"\n需要安装的包:")
            for pkg in packages_to_install:
                print(f"   📦 {pkg}")
            
            print(f"\n是否继续安装？(y/n): ", end='')
            response = input().strip().lower()
            if response not in ['y', 'yes', '是']:
                print("安装已取消")
                return False
            
            # 7. 开始安装
            print(f"\n🚀 开始安装依赖包...")
            success_count = 0
            failed_packages = []
            
            for i, package in enumerate(packages_to_install, 1):
                print(f"\n进度: [{i}/{len(packages_to_install)}]")
                if self.install_package(package):
                    success_count += 1
                else:
                    failed_packages.append(package)
            
            # 8. 显示安装结果
            print(f"\n" + "=" * 60)
            print(f"安装完成!")
            print(f"✅ 成功: {success_count} 个包")
            print(f"❌ 失败: {len(failed_packages)} 个包")
            
            if failed_packages:
                print(f"\n失败的包:")
                for pkg in failed_packages:
                    print(f"   ❌ {pkg}")
                print(f"\n您可以稍后手动安装这些包：")
                for pkg in failed_packages:
                    print(f"   pip install {pkg}")
            
            return len(failed_packages) == 0
        else:
            print(f"\n🎉 所有依赖包都已安装！")
            return True
    
    def save_installation_log(self):
        """保存安装日志"""
        log_file = Path("installation_log.json")
        log_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'platform': self.platform_info,
            'installation_log': self.installation_log
        }
        
        try:
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            print(f"📝 安装日志已保存到: {log_file}")
        except Exception as e:
            print(f"⚠️  保存安装日志失败: {e}")

def main():
    """主函数"""
    installer = DependencyInstaller()
    
    try:
        success = installer.install_dependencies()
        installer.save_installation_log()
        
        if success:
            print(f"\n🎉 依赖安装完成！现在可以运行股票筛选器了。")
            print(f"运行命令: python src/main.py")
        else:
            print(f"\n⚠️  依赖安装部分失败，请检查错误信息并手动安装失败的包。")
        
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print(f"\n\n⏹️  安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程出现错误: {e}")
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()