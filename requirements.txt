# 股票筛选器依赖包
# GUI framework - tkinter is included with Python standard library

# Windows automation
pywinauto>=0.6.8

# OCR processing
paddleocr>=3.0
paddlepaddle>=2.5.0
opencv-python>=4.8.0
Pillow>=10.0.0

# Screen capture
mss>=9.0.1

# Data processing
pandas>=2.0.0
openpyxl>=3.1.0
xlrd>=2.0.0

# System utilities
psutil>=5.9.0

# HTTP requests for notifications
requests>=2.31.0

# Global keyboard listener
keyboard>=0.13.5

# Web automation
playwright>=1.40.0

# HTML parsing
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Image processing
numpy>=1.24.0

# Additional dependencies for EasyOCR
torch>=2.0.0
torchvision>=0.15.0

# Optional: for better OCR performance
# tensorflow>=2.12.0  # Alternative to torch, uncomment if preferred

# Development dependencies (optional)
# pytest>=7.0.0
# black>=23.0.0
# flake8>=6.0.0