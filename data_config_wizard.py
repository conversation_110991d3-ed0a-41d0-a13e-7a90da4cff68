# -*- coding: utf-8 -*-
"""
数据元素专用配置向导
专门用于配置股票数据表格和列元素的选择器，并支持HTML数据处理和格式化配置
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import logging
import asyncio
import json
from typing import Dict, Optional, Callable
from web_element_selector import get_web_element_selector_manager
from html_data_parser import create_html_data_parser_manager


def safe_js_string(text: str) -> str:
    """安全地转义字符串用于JavaScript代码"""
    if not text:
        return '""'
    
    # 使用JSON序列化来安全地处理字符串
    # 这会自动处理所有特殊字符的转义
    return json.dumps(str(text), ensure_ascii=False)


def safe_js_call(func_name: str, *args) -> str:
    """安全地生成JavaScript函数调用"""
    safe_args = [safe_js_string(arg) if isinstance(arg, str) else str(arg) for arg in args]
    return f"{func_name}({', '.join(safe_args)});"


class DataElementConfigWizard:
    """数据元素配置向导"""
    
    def __init__(self, parent_window: tk.Tk, callback: Optional[Callable] = None):
        """
        初始化数据配置向导
        
        Args:
            parent_window: 父窗口
            callback: 配置完成后的回调函数
        """
        self.parent_window = parent_window
        self.callback = callback
        self.logger = logging.getLogger(__name__)
        
        # 窗口相关
        self.wizard_window = None
        self.is_configuring = False
        self.config_thread = None
        
        # 配置相关
        self.selector_manager = get_web_element_selector_manager()
        self.html_parser_manager = create_html_data_parser_manager()
        self.selected_elements = {}
        
        # HTML数据处理状态
        self.is_processing_html = False
        self.html_data_result = None
        
        # 数据元素配置步骤（不包含登录相关）
        self.data_config_steps = [
            {"name": "data_table", "desc": "数据表格", "status": "待配置", 
             "hint": "点击包含股票数据的整个表格区域"},
            {"name": "stock_code_column", "desc": "股票代码列", "status": "待配置",
             "hint": "点击表格中股票代码列的任意一个单元格（如：000001）"},
            {"name": "stock_name_column", "desc": "股票名称列", "status": "待配置",
             "hint": "点击表格中股票名称列的任意一个单元格（如：平安银行）"},
            {"name": "xiaocao_jingwang_column", "desc": "小草竞王列", "status": "待配置",
             "hint": "点击包含'小草竞王'数据的列中的任意一个单元格"},
            {"name": "xiaocao_hongpan_column", "desc": "小草红盘起爆列", "status": "待配置",
             "hint": "点击包含'小草红盘起爆'数据的列中的任意一个单元格"},
            {"name": "xiaocao_lvpan_column", "desc": "小草绿盘低吸列", "status": "待配置",
             "hint": "点击包含'小草绿盘低吸'数据的列中的任意一个单元格"},
            {"name": "xiaocao_lianban_column", "desc": "小草连板接力列", "status": "待配置",
             "hint": "点击包含'小草连板接力'数据的列中的任意一个单元格"},
        ]
    
    def show_wizard(self):
        """显示数据配置向导窗口"""
        if self.wizard_window:
            self.wizard_window.lift()
            return
        
        self.wizard_window = tk.Toplevel(self.parent_window)
        self.wizard_window.title("数据元素配置向导")
        self.wizard_window.geometry("900x700")
        self.wizard_window.resizable(True, True)
        
        # 设置窗口属性
        self.wizard_window.transient(self.parent_window)
        self.wizard_window.grab_set()
        
        # 创建界面
        self._create_wizard_interface()
        
        # 加载现有配置
        self._load_existing_config()
        
        # 居中显示
        self._center_window()
    
    def _create_wizard_interface(self):
        """创建向导界面"""
        # 主框架
        main_frame = ttk.Frame(self.wizard_window, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.wizard_window.columnconfigure(0, weight=1)
        self.wizard_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="股票数据元素配置向导", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 说明文字
        desc_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="10")
        desc_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        desc_frame.columnconfigure(0, weight=1)
        
        desc_text = (
            "📋 此向导专门用于配置股票数据表格和各列的选择器\n\n"
            "🔧 配置前请确保：\n"
            "   • 您已经手动登录到股票数据网站\n"
            "   • 数据页面已完全加载，能看到包含'小草竞王'等数据的表格\n"
            "   • 表格中有实际的股票数据显示\n\n"
            "📌 配置说明：\n"
            "   • 点击'开始配置数据元素'将自动打开浏览器\n"
            "   • 在浏览器中按照右上角指导面板的提示操作\n"
            "   • 系统会逐步引导您选择每个数据元素\n"
            "   • 完成后配置将自动保存\n\n"
            "⚠️ 重要：配置过程中请保持浏览器窗口在前景，不要关闭此向导窗口"
        )
        
        desc_label = ttk.Label(desc_frame, text=desc_text, justify=tk.LEFT, 
                              font=("Arial", 9))
        desc_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 配置步骤列表
        steps_frame = ttk.LabelFrame(main_frame, text="配置步骤", padding="10")
        steps_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                        pady=(0, 15))
        steps_frame.columnconfigure(0, weight=1)
        steps_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ("desc", "status")
        self.steps_tree = ttk.Treeview(steps_frame, columns=columns, show="headings", height=8)
        self.steps_tree.heading("desc", text="配置项目")
        self.steps_tree.heading("status", text="状态")
        
        self.steps_tree.column("desc", width=300)
        self.steps_tree.column("status", width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(steps_frame, orient=tk.VERTICAL, command=self.steps_tree.yview)
        self.steps_tree.configure(yscrollcommand=scrollbar.set)
        
        self.steps_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 添加配置步骤到树视图
        for step in self.data_config_steps:
            item = self.steps_tree.insert("", tk.END, 
                                         values=(step["desc"], step["status"]),
                                         tags=(step["name"],))
        
        # 进度条
        self.progress_var = tk.IntVar()
        self.progress = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                       maximum=len(self.data_config_steps))
        self.progress.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="准备就绪", foreground="blue")
        self.status_label.grid(row=4, column=0, columnspan=2, pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(10, 0))
        
        # 第一行按钮 - 元素配置
        button_row1 = ttk.Frame(button_frame)
        button_row1.pack(fill=tk.X, pady=(0, 5))
        
        # 开始配置按钮
        self.start_config_btn = ttk.Button(button_row1, text="开始配置数据元素", 
                                          command=self.start_data_configuration,
                                          style="Accent.TButton")
        self.start_config_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 测试配置按钮
        self.test_config_btn = ttk.Button(button_row1, text="测试配置", 
                                         command=self.test_configuration,
                                         state=tk.DISABLED)
        self.test_config_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 保存配置按钮
        self.save_config_btn = ttk.Button(button_row1, text="保存配置", 
                                         command=self.save_configuration,
                                         state=tk.DISABLED)
        self.save_config_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重置配置按钮
        self.reset_config_btn = ttk.Button(button_row1, text="重置配置", 
                                          command=self.reset_configuration)
        self.reset_config_btn.pack(side=tk.LEFT)
        
        # 第二行按钮 - HTML数据处理
        button_row2 = ttk.Frame(button_frame)
        button_row2.pack(fill=tk.X, pady=(5, 0))
        
        # HTML数据处理按钮
        self.process_html_btn = ttk.Button(button_row2, text="处理HTML数据", 
                                          command=self.process_html_data)
        self.process_html_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 验证HTML格式按钮
        self.validate_html_btn = ttk.Button(button_row2, text="验证HTML格式", 
                                           command=self.validate_html_format)
        self.validate_html_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 导出处理结果按钮
        self.export_result_btn = ttk.Button(button_row2, text="导出处理结果", 
                                           command=self.export_processing_result,
                                           state=tk.DISABLED)
        self.export_result_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 关闭按钮
        close_btn = ttk.Button(button_frame, text="关闭", command=self.close_wizard)
        close_btn.pack(side=tk.RIGHT)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="配置日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                      pady=(15, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, height=6, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, 
                                     command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 扩展主框架中的行权重
        main_frame.rowconfigure(6, weight=1)
    
    def _load_existing_config(self):
        """加载现有配置"""
        try:
            existing_config = self.selector_manager.load_selectors_from_config()
            if existing_config:
                # 只加载数据相关的配置
                data_elements = {name: selector for name, selector in existing_config.items() 
                               if name in [step["name"] for step in self.data_config_steps]}
                self.selected_elements = data_elements
                self._update_steps_status()
                self.log_message("已加载现有数据元素配置")
                if data_elements:
                    self.save_config_btn.config(state=tk.NORMAL)
                    self.test_config_btn.config(state=tk.NORMAL)  # 启用测试按钮
        except Exception as e:
            self.logger.error(f"加载现有配置失败: {str(e)}")
    
    def _update_steps_status(self):
        """更新配置步骤状态"""
        configured_count = 0
        for item in self.steps_tree.get_children():
            item_tags = self.steps_tree.item(item, "tags")
            if item_tags:
                step_name = item_tags[0]
                if step_name in self.selected_elements:
                    self.steps_tree.item(item, values=(
                        self.steps_tree.item(item, "values")[0], "已配置"
                    ))
                    configured_count += 1
                else:
                    self.steps_tree.item(item, values=(
                        self.steps_tree.item(item, "values")[0], "待配置"
                    ))
        
        self.progress_var.set(configured_count)
    
    def start_data_configuration(self):
        """开始数据元素配置"""
        if self.is_configuring:
            messagebox.showwarning("提示", "配置正在进行中，请稍候...")
            return
        
        # 确认开始配置
        result = messagebox.askyesno("确认配置", 
                                   "开始配置数据元素前，请确认：\n\n"
                                   "✓ 您已经手动登录到股票数据网站\n"
                                   "✓ 当前页面显示包含'小草竞王'等数据的表格\n"
                                   "✓ 表格中有实际的股票数据\n\n"
                                   "配置过程中请按照提示点击对应的页面元素。\n"
                                   "确认开始配置吗？")
        if not result:
            return
        
        self.is_configuring = True
        self.start_config_btn.config(state=tk.DISABLED)
        self.reset_config_btn.config(state=tk.DISABLED)
        self.status_label.config(text="正在配置中...", foreground="orange")
        
        # 在后台线程中运行配置
        self.config_thread = threading.Thread(target=self._run_data_configuration, daemon=True)
        self.config_thread.start()
    
    def _run_data_configuration(self):
        """运行数据配置流程（后台线程）"""
        try:
            self.log_message("🚀 开始数据元素配置...")
            
            # 创建专门的数据选择器
            from web_element_selector import WebElementSelector
            
            # 使用回调函数实时更新进度  
            data_selector = DataElementSelector(
                config_steps=self.data_config_steps,
                progress_callback=self._on_step_completed
            )
            
            # 启动数据元素选择
            self.log_message("📱 正在启动浏览器...")
            result = await_sync(data_selector.start_selection())
            
            # 更新UI（需要在主线程中执行）
            self.wizard_window.after(0, self._on_data_configuration_completed, result)
            
        except Exception as e:
            error_msg = f"数据配置过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.logger.exception("详细错误信息:")  # 记录完整的异常堆栈
            
            # 在GUI中显示用户友好的错误信息
            user_friendly_error = self._get_user_friendly_error_message(str(e))
            self.wizard_window.after(0, self._on_configuration_error, user_friendly_error)
    
    def _get_user_friendly_error_message(self, error_str: str) -> str:
        """将技术错误信息转换为用户友好的错误信息"""
        if "timeout" in error_str.lower():
            return "配置超时：请检查网络连接和浏览器状态，然后重试"
        elif "browser" in error_str.lower() or "playwright" in error_str.lower():
            return "浏览器启动失败：请确保系统支持自动化浏览器，或尝试重启应用程序"
        elif "element" in error_str.lower():
            return "元素选择失败：请确保页面已完全加载，并包含需要配置的数据表格"
        elif "permission" in error_str.lower():
            return "权限错误：请以管理员权限运行程序，或检查防火墙设置"
        elif "network" in error_str.lower():
            return "网络连接错误：请检查网络连接，确保可以访问目标网站"
        else:
            return f"配置过程中发生未知错误：{error_str[:100]}..."
    
    def _on_step_completed(self, step_name: str, step_data: dict):
        """单个步骤完成时的回调"""
        def update_ui():
            if step_data:
                self.selected_elements[step_name] = step_data
                self._update_steps_status()
                step_desc = next((s["desc"] for s in self.data_config_steps 
                                if s["name"] == step_name), step_name)
                
                # 计算当前进度
                current_progress = len(self.selected_elements)
                total_steps = len(self.data_config_steps)
                
                self.update_status_with_progress(
                    f"✓ 已配置: {step_desc}", 
                    current_progress
                )
            else:
                step_desc = next((s["desc"] for s in self.data_config_steps 
                                if s["name"] == step_name), step_name)
                self.log_message(f"❌ 跳过配置: {step_desc}")
        
        self.wizard_window.after(0, update_ui)
    
    def _on_data_configuration_completed(self, result):
        """数据配置完成回调（主线程）"""
        self.is_configuring = False
        self.start_config_btn.config(state=tk.NORMAL)
        self.reset_config_btn.config(state=tk.NORMAL)
        
        if result:
            self.selected_elements.update(result)
            
            # 更新进度为最大值
            self.progress_var.set(len(self.data_config_steps))
            
            self._update_steps_status()
            self.save_config_btn.config(state=tk.NORMAL)
            self.test_config_btn.config(state=tk.NORMAL)  # 启用测试按钮
            
            # 使用改进的状态更新
            self.update_status_with_progress(
                f"🎉 数据元素配置完成！共配置了 {len(result)} 个元素",
                len(self.data_config_steps)
            )
            
            # 显示配置结果
            self._show_config_result()
        else:
            self.status_label.config(text="配置失败或取消", foreground="red")
            self.log_message("❌ 数据配置失败或被用户取消")
    
    def _on_configuration_error(self, error_msg: str):
        """配置错误回调（主线程）"""
        self.is_configuring = False
        self.start_config_btn.config(state=tk.NORMAL)
        self.reset_config_btn.config(state=tk.NORMAL)
        self.status_label.config(text="配置失败", foreground="red")
        self.log_message(f"❌ 配置失败: {error_msg}")
        messagebox.showerror("配置失败", error_msg)
    
    def test_configuration(self):
        """测试配置的有效性"""
        if not self.selected_elements:
            messagebox.showwarning("提示", "没有配置信息可测试")
            return
        
        # 创建测试线程
        test_thread = threading.Thread(target=self._run_configuration_test, daemon=True)
        test_thread.start()
        
        # 禁用测试按钮防止重复点击
        self.test_config_btn.config(state=tk.DISABLED)
        self.log_message("🧪 开始测试配置...")
    
    def _run_configuration_test(self):
        """运行配置测试（后台线程）"""
        try:
            # 验证配置完整性
            validation_result = self._validate_configuration()
            if not validation_result['valid']:
                self.wizard_window.after(0, self._on_test_completed, False, 
                                       f"配置验证失败: {validation_result['error']}")
                return
            
            # 创建测试选择器
            test_selector = ConfigurationTester(self.selected_elements)
            
            # 运行实际测试
            test_result = await_sync(test_selector.test_selectors())
            
            # 更新UI
            self.wizard_window.after(0, self._on_test_completed, 
                                   test_result['success'], test_result['message'])
            
        except Exception as e:
            error_msg = f"配置测试过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.wizard_window.after(0, self._on_test_completed, False, error_msg)
    
    def _validate_configuration(self):
        """验证配置完整性"""
        required_elements = [step['name'] for step in self.data_config_steps]
        missing_elements = []
        
        for element_name in required_elements:
            if element_name not in self.selected_elements:
                missing_elements.append(element_name)
        
        if missing_elements:
            missing_descriptions = []
            for name in missing_elements:
                desc = next((s['desc'] for s in self.data_config_steps if s['name'] == name), name)
                missing_descriptions.append(desc)
            
            return {
                'valid': False,
                'error': f"缺少必需的配置项: {', '.join(missing_descriptions)}"
            }
        
        # 验证选择器格式
        for name, selector in self.selected_elements.items():
            if not selector or not isinstance(selector, str):
                desc = next((s['desc'] for s in self.data_config_steps if s['name'] == name), name)
                return {
                    'valid': False,
                    'error': f"配置项'{desc}'的选择器格式无效"
                }
        
        return {'valid': True, 'error': None}
    
    def _on_test_completed(self, success: bool, message: str):
        """测试完成回调（主线程）"""
        self.test_config_btn.config(state=tk.NORMAL)
        
        if success:
            self.log_message(f"✅ 配置测试成功: {message}")
            messagebox.showinfo("测试成功", f"配置测试通过！\n\n{message}")
        else:
            self.log_message(f"❌ 配置测试失败: {message}")
            messagebox.showerror("测试失败", f"配置测试失败！\n\n{message}\n\n请检查配置或重新配置。")
    
    def save_configuration(self):
        """保存配置到文件"""
        try:
            if not self.selected_elements:
                messagebox.showwarning("提示", "没有配置信息可保存")
                return
            
            # 先验证配置
            validation_result = self._validate_configuration()
            if not validation_result['valid']:
                messagebox.showerror("验证失败", f"配置验证失败，无法保存:\n\n{validation_result['error']}")
                return
            
            # 加载现有的完整配置
            existing_config = self.selector_manager.load_selectors_from_config() or {}
            
            # 更新数据元素部分
            existing_config.update(self.selected_elements)
            
            # 保存完整配置
            self.selector_manager.save_selectors_to_config(existing_config)
            
            self.log_message("✅ 配置已保存")
            messagebox.showinfo("成功", "数据元素配置已保存！")
            
            # 调用回调函数
            if self.callback:
                self.callback(self.selected_elements)
            
        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            self.logger.error(error_msg)
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)
    
    def reset_configuration(self):
        """重置配置"""
        result = messagebox.askyesno("确认重置", "确定要重置所有数据元素配置吗？\n此操作不可撤销。")
        if result:
            self.selected_elements = {}
            self.progress_var.set(0)
            self.status_label.config(text="配置已重置", foreground="blue")
            self.save_config_btn.config(state=tk.DISABLED)
            self._update_steps_status()
            self.log_message("配置已重置")
    
    def _show_config_result(self):
        """显示配置结果"""
        if not self.selected_elements:
            return
        
        result_window = tk.Toplevel(self.wizard_window)
        result_window.title("配置结果")
        result_window.geometry("600x400")
        result_window.transient(self.wizard_window)
        result_window.grab_set()
        
        # 创建结果显示
        frame = ttk.Frame(result_window, padding="15")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="数据元素配置结果", font=("Arial", 14, "bold")).pack(pady=(0, 10))
        
        # 结果文本框
        text_widget = tk.Text(frame, wrap=tk.WORD, height=15)
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 填充结果内容
        for step in self.data_config_steps:
            step_name = step["name"]
            step_desc = step["desc"]
            if step_name in self.selected_elements:
                selector = self.selected_elements[step_name]
                if isinstance(selector, dict) and 'selector' in selector:
                    selector = selector['selector']
                text_widget.insert(tk.END, f"✓ {step_desc}:\n  {selector}\n\n")
            else:
                text_widget.insert(tk.END, f"✗ {step_desc}: 未配置\n\n")
        
        text_widget.config(state=tk.DISABLED)
        
        # 关闭按钮
        ttk.Button(result_window, text="关闭", 
                  command=result_window.destroy).pack(pady=10)
    
    def log_message(self, message: str):
        """添加日志消息并更新状态"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 同时更新状态标签（提取关键信息）
        if "🚀 开始" in message:
            self.status_label.config(text="正在启动配置...", foreground="blue")
        elif "📱 正在启动浏览器" in message:
            self.status_label.config(text="正在启动浏览器...", foreground="orange")
        elif "✓ 已选择" in message:
            self.status_label.config(text="元素选择成功", foreground="green")
        elif "❌" in message or "错误" in message:
            self.status_label.config(text="配置过程中出现错误", foreground="red")
        elif "🎉" in message or "完成" in message:
            self.status_label.config(text="配置完成", foreground="green")
    
    def update_status_with_progress(self, message: str, progress: int = None):
        """更新状态并可选地更新进度"""
        self.log_message(message)
        if progress is not None:
            self.progress_var.set(progress)
    
    def _center_window(self):
        """窗口居中显示"""
        self.wizard_window.update_idletasks()
        width = self.wizard_window.winfo_width()
        height = self.wizard_window.winfo_height()
        x = (self.wizard_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.wizard_window.winfo_screenheight() // 2) - (height // 2)
        self.wizard_window.geometry(f"{width}x{height}+{x}+{y}")
    
    def close_wizard(self):
        """关闭向导"""
        if self.is_configuring:
            result = messagebox.askyesno("确认关闭", "配置正在进行中，确定要关闭吗？")
            if not result:
                return
        
        if self.wizard_window:
            self.wizard_window.destroy()
            self.wizard_window = None
    
    def process_html_data(self):
        """处理HTML数据"""
        if self.is_processing_html:
            messagebox.showwarning("提示", "HTML数据处理正在进行中，请稍候...")
            return
        
        # 请求用户选择HTML数据来源
        result = messagebox.askyesnocancel("HTML数据处理", 
                                         "请选择HTML数据来源：\n\n"
                                         "• 点击【是】：从文件加载HTML\n"
                                         "• 点击【否】：手动粘贴HTML内容\n"
                                         "• 点击【取消】：取消操作")
        
        if result is None:  # 用户点击取消
            return
        elif result is True:  # 从文件加载
            self._process_html_from_file()
        else:  # 手动输入
            self._process_html_from_input()
    
    def _process_html_from_file(self):
        """从文件处理HTML数据"""
        file_path = filedialog.askopenfilename(
            title="选择HTML文件",
            filetypes=[("HTML files", "*.html *.htm"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            self._run_html_processing(html_content)
            
        except Exception as e:
            error_msg = f"读取HTML文件失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("文件读取失败", error_msg)
    
    def _process_html_from_input(self):
        """从用户输入处理HTML数据"""
        # 创建HTML输入对话框
        input_dialog = HTMLDataInputDialog(self.wizard_window, self._run_html_processing)
        input_dialog.show()
    
    def _run_html_processing(self, html_content: str):
        """运行HTML数据处理"""
        if not html_content.strip():
            messagebox.showwarning("提示", "HTML内容为空")
            return
        
        self.is_processing_html = True
        self.process_html_btn.config(state=tk.DISABLED)
        self.status_label.config(text="正在处理HTML数据...", foreground="orange")
        self.log_message("开始处理HTML数据...")
        
        # 在后台线程中运行处理
        processing_thread = threading.Thread(
            target=self._process_html_background,
            args=(html_content,),
            daemon=True
        )
        processing_thread.start()
    
    def _process_html_background(self, html_content: str):
        """后台处理HTML数据（后台线程）"""
        try:
            # 首先验证HTML结构
            validation_result = self.html_parser_manager.validate_html_structure(html_content)
            
            if not validation_result['is_valid']:
                error_msg = f"HTML结构验证失败: {validation_result.get('error', '未知错误')}"
                self.wizard_window.after(0, self._on_html_processing_error, error_msg)
                return
            
            # 提取股票数据
            stock_data = self.html_parser_manager.parse_stock_data_from_html(html_content)
            
            # 处理结果
            result = {
                'validation': validation_result,
                'stock_data': stock_data,
                'html_content': html_content
            }
            
            # 更新UI（需要在主线程中执行）
            self.wizard_window.after(0, self._on_html_processing_completed, result)
            
        except Exception as e:
            error_msg = f"HTML数据处理过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.wizard_window.after(0, self._on_html_processing_error, error_msg)
    
    def _on_html_processing_completed(self, result: Dict):
        """HTML数据处理完成回调（主线程）"""
        self.is_processing_html = False
        self.process_html_btn.config(state=tk.NORMAL)
        self.html_data_result = result
        
        stock_data = result['stock_data']
        validation = result['validation']
        
        if stock_data:
            self.status_label.config(text="HTML数据处理成功", foreground="green")
            self.export_result_btn.config(state=tk.NORMAL)
            
            self.log_message(f"✅ HTML数据处理成功")
            self.log_message(f"   - 验证通过，找到 {validation['total_columns']} 列")
            self.log_message(f"   - 提取到 {len(stock_data)} 只股票数据")
            
            # 显示前几条数据作为预览
            preview_count = min(3, len(stock_data))
            self.log_message("数据预览:")
            for i, data in enumerate(stock_data[:preview_count]):
                self.log_message(f"  {i+1}. {data.get('stock_code', 'N/A')} {data.get('stock_name', 'N/A')} - "
                               f"竞王:{data.get('jingwang', 'N/A')} 连板接力:{data.get('lianban_jieli', 'N/A')}")
            
            if len(stock_data) > preview_count:
                self.log_message(f"  ... 还有 {len(stock_data) - preview_count} 条数据")
            
        else:
            self.status_label.config(text="未提取到数据", foreground="red")
            self.log_message("❌ 未提取到任何股票数据")
    
    def _on_html_processing_error(self, error_msg: str):
        """HTML数据处理错误回调（主线程）"""
        self.is_processing_html = False
        self.process_html_btn.config(state=tk.NORMAL)
        self.status_label.config(text="HTML数据处理失败", foreground="red")
        self.log_message(f"❌ {error_msg}")
        messagebox.showerror("处理失败", error_msg)
    
    def validate_html_format(self):
        """验证HTML格式"""
        # 请求用户选择HTML数据来源
        result = messagebox.askyesnocancel("HTML格式验证", 
                                         "请选择HTML数据来源：\n\n"
                                         "• 点击【是】：从文件加载HTML\n"
                                         "• 点击【否】：手动粘贴HTML内容\n"
                                         "• 点击【取消】：取消验证")
        
        if result is None:  # 用户点击取消
            return
        elif result is True:  # 从文件加载
            self._validate_html_from_file()
        else:  # 手动输入
            self._validate_html_from_input()
    
    def _validate_html_from_file(self):
        """从文件验证HTML格式"""
        file_path = filedialog.askopenfilename(
            title="选择HTML文件",
            filetypes=[("HTML files", "*.html *.htm"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            self._run_html_validation(html_content)
            
        except Exception as e:
            error_msg = f"读取HTML文件失败: {str(e)}"
            self.logger.error(error_msg)
            messagebox.showerror("文件读取失败", error_msg)
    
    def _validate_html_from_input(self):
        """从用户输入验证HTML格式"""
        # 创建HTML输入对话框
        input_dialog = HTMLDataInputDialog(self.wizard_window, self._run_html_validation)
        input_dialog.show()
    
    def _run_html_validation(self, html_content: str):
        """运行HTML格式验证"""
        if not html_content.strip():
            messagebox.showwarning("提示", "HTML内容为空")
            return
        
        try:
            validation_result = self.html_parser_manager.validate_html_structure(html_content)
            
            if validation_result['is_valid']:
                self.log_message("✅ HTML格式验证通过")
                self.log_message(f"   - 找到表头: {validation_result['header_found']}")
                self.log_message(f"   - 找到数据行: {validation_result['data_rows_found']}")
                self.log_message(f"   - 总列数: {validation_result['total_columns']}")
                self.log_message(f"   - 总行数: {validation_result['total_rows']}")
                self.log_message(f"   - 目标列: {', '.join(validation_result['target_columns_found'])}")
                
                if validation_result['missing_columns']:
                    self.log_message(f"   ⚠️ 缺少列: {', '.join(validation_result['missing_columns'])}")
                
                messagebox.showinfo("验证成功", "HTML格式验证通过！\n\n"
                                  f"找到 {validation_result['total_columns']} 列，{validation_result['total_rows']} 行数据\n"
                                  f"目标列: {', '.join(validation_result['target_columns_found'])}")
            else:
                self.log_message("❌ HTML格式验证失败")
                if 'error' in validation_result:
                    self.log_message(f"   错误: {validation_result['error']}")
                else:
                    self.log_message(f"   - 找到表头: {validation_result.get('header_found', False)}")
                    self.log_message(f"   - 找到数据行: {validation_result.get('data_rows_found', False)}")
                    if validation_result.get('missing_columns'):
                        self.log_message(f"   - 缺少列: {', '.join(validation_result['missing_columns'])}")
                
                messagebox.showerror("验证失败", "HTML格式验证失败！\n\n"
                                   "请检查HTML内容是否包含正确的表格结构。")
                
        except Exception as e:
            error_msg = f"HTML格式验证失败: {str(e)}"
            self.logger.error(error_msg)
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("验证失败", error_msg)
    
    def export_processing_result(self):
        """导出HTML处理结果"""
        if not self.html_data_result or not self.html_data_result.get('stock_data'):
            messagebox.showwarning("提示", "没有可导出的处理结果")
            return
        
        # 请求用户选择导出文件路径
        file_path = filedialog.asksaveasfilename(
            title="保存处理结果",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            html_content = self.html_data_result['html_content']
            success = self.html_parser_manager.export_to_excel(html_content, file_path)
            
            if success:
                self.status_label.config(text="处理结果导出成功", foreground="green")
                stock_count = len(self.html_data_result['stock_data'])
                self.log_message(f"✅ 处理结果已导出到: {file_path}")
                self.log_message(f"   包含 {stock_count} 只股票的数据")
                messagebox.showinfo("导出成功", f"处理结果已成功导出到:\n{file_path}\n\n"
                                  f"包含 {stock_count} 只股票的数据")
            else:
                self.status_label.config(text="处理结果导出失败", foreground="red")
                self.log_message("❌ 处理结果导出失败")
                messagebox.showerror("导出失败", "处理结果导出失败，请检查文件路径和权限")
                
        except Exception as e:
            error_msg = f"导出处理结果失败: {str(e)}"
            self.logger.error(error_msg)
            self.status_label.config(text="处理结果导出失败", foreground="red")
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导出失败", error_msg)


class DataElementSelector:
    """数据元素选择器"""
    
    def __init__(self, config_steps: list, progress_callback: Optional[Callable] = None):
        self.config_steps = config_steps
        self.progress_callback = progress_callback
        self.logger = logging.getLogger(__name__)
        
        # 创建专门的WebElementSelector实例
        from web_element_selector import WebElementSelector
        
        # 创建临时配置步骤，只包含数据相关元素
        self.data_config_steps_for_selector = []
        for step in config_steps:
            self.data_config_steps_for_selector.append({
                'name': step['name'],
                'description': step['desc'],
                'hint': step['hint']
            })
    
    async def start_selection(self):
        """开始数据元素选择"""
        try:
            # 创建WebElementSelector实例，但只配置数据元素
            selector = DataOnlyWebElementSelector(
                config_steps=self.data_config_steps_for_selector,
                progress_callback=self.progress_callback
            )
            
            # 启动选择流程
            result = await selector.start_selection()
            return result
            
        except Exception as e:
            self.logger.error(f"数据元素选择失败: {str(e)}")
            return None


class DataOnlyWebElementSelector:
    """专门用于数据元素的选择器"""
    
    def __init__(self, config_steps: list, progress_callback: Optional[Callable] = None):
        self.config_steps = config_steps
        self.progress_callback = progress_callback
        self.logger = logging.getLogger(__name__)
        
        self.browser = None
        self.context = None
        self.page = None
        self.playwright = None
        self.selected_elements = {}
    
    async def start_selection(self):
        """开始选择流程"""
        try:
            # 初始化浏览器
            await self._initialize_browser()
            
            # 注入高亮脚本
            await self._inject_highlight_script()
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 在浏览器中显示配置指导
            await self._show_browser_guidance()
            
            # 开始逐步选择流程
            await self._start_step_by_step_selection()
            
            return self.selected_elements
            
        except Exception as e:
            self.logger.error(f"数据元素选择失败: {str(e)}")
            return None
        finally:
            await self._cleanup()
    
    async def _initialize_browser(self):
        """初始化浏览器"""
        try:
            from playwright.async_api import async_playwright
            
            self.logger.info("正在启动Playwright...")
            self.playwright = await async_playwright().start()
            
            self.logger.info("正在启动Chromium浏览器...")
            self.browser = await self.playwright.chromium.launch(
                headless=False,
                timeout=30000,
                args=['--no-sandbox', '--disable-dev-shm-usage']  # 添加稳定性参数
            )
            
            self.logger.info("正在创建浏览器上下文...")
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            )
            
            self.logger.info("正在创建新页面...")
            self.page = await self.context.new_page()
            
            # 设置更长的超时时间
            self.page.set_default_timeout(60000)
            
            # 导航到目标页面
            from config import WEB_AUTOMATION_CONFIG
            target_url = WEB_AUTOMATION_CONFIG.get('target_url', 'https://www.topxlc.com/ddcj-yqs-xc/web/')
            
            self.logger.info(f"正在导航到目标页面: {target_url}")
            await self.page.goto(target_url, wait_until='domcontentloaded')
            
            # 等待页面加载稳定
            await self.page.wait_for_load_state('networkidle', timeout=30000)
            
            self.logger.info("浏览器初始化完成")
            
        except Exception as e:
            error_msg = f"浏览器初始化失败: {str(e)}"
            self.logger.error(error_msg)
            await self._cleanup()  # 确保清理资源
            raise Exception(error_msg)
    
    async def _inject_highlight_script(self):
        """注入元素高亮脚本"""
        # 使用与原有系统相同的高亮脚本
        highlight_script = """
        (function() {
            window.currentHighlighted = null;
            window.selectedElement = null;
            window.elementSelectCallback = null;
            
            // 高亮元素
            function highlightElement(element) {
                if (window.currentHighlighted) {
                    removeHighlight(window.currentHighlighted);
                }
                // 更强烈的高亮效果
                element.style.outline = '4px solid #ff4444';
                element.style.backgroundColor = 'rgba(255, 68, 68, 0.2)';
                element.style.cursor = 'pointer';
                element.style.boxShadow = '0 0 15px rgba(255, 68, 68, 0.6)';
                element.style.transform = 'scale(1.02)';
                element.style.transition = 'all 0.2s ease';
                element.style.zIndex = '999';
                window.currentHighlighted = element;
                
                // 添加闪烁动画
                element.style.animation = 'elementPulse 1s infinite';
                
                // 创建动画样式（如果不存在）
                if (!document.getElementById('elementHighlightStyle')) {
                    const style = document.createElement('style');
                    style.id = 'elementHighlightStyle';
                    style.textContent = `
                        @keyframes elementPulse {
                            0% { box-shadow: 0 0 15px rgba(255, 68, 68, 0.6); }
                            50% { box-shadow: 0 0 25px rgba(255, 68, 68, 0.9); }
                            100% { box-shadow: 0 0 15px rgba(255, 68, 68, 0.6); }
                        }
                    `;
                    document.head.appendChild(style);
                }
            }
            
            // 移除高亮
            function removeHighlight(element) {
                element.style.outline = '';
                element.style.backgroundColor = '';
                element.style.cursor = '';
                element.style.boxShadow = '';
                element.style.transform = '';
                element.style.transition = '';
                element.style.zIndex = '';
                element.style.animation = '';
            }
            
            // 生成元素选择器（简化版本）
            function generateSelectors(element) {
                const selectors = [];
                const tagName = element.tagName.toLowerCase();
                
                // ID选择器
                if (element.id) {
                    selectors.push({
                        type: 'id',
                        selector: '#' + element.id,
                        priority: 1
                    });
                }
                
                // Class选择器
                if (element.className && typeof element.className === 'string') {
                    const classes = element.className.trim().split(/\\s+/).filter(c => c);
                    if (classes.length > 0) {
                        selectors.push({
                            type: 'class',
                            selector: '.' + classes.join('.'),
                            priority: 2
                        });
                    }
                }
                
                // CSS路径选择器
                const cssPath = getCSSPath(element);
                if (cssPath) {
                    selectors.push({
                        type: 'css_path',
                        selector: cssPath,
                        priority: 3
                    });
                }
                
                return selectors.sort((a, b) => a.priority - b.priority);
            }
            
            // 生成CSS路径
            function getCSSPath(element) {
                if (element === document.body) return 'body';
                
                const names = [];
                while (element && element.nodeType === Node.ELEMENT_NODE) {
                    let selector = element.nodeName.toLowerCase();
                    
                    if (element.id) {
                        selector += '#' + element.id;
                        names.unshift(selector);
                        break;
                    } else {
                        let sibling = element;
                        let nth = 1;
                        while ((sibling = sibling.previousElementSibling)) {
                            if (sibling.nodeName.toLowerCase() === selector) nth++;
                        }
                        if (nth !== 1) selector += ':nth-child(' + nth + ')';
                    }
                    
                    names.unshift(selector);
                    element = element.parentNode;
                }
                
                return names.join(' > ');
            }
            
            // 鼠标悬停事件
            document.addEventListener('mouseover', function(e) {
                if (!window.isSelecting) return;
                e.preventDefault();
                e.stopPropagation();
                highlightElement(e.target);
            });
            
            // 鼠标点击事件
            document.addEventListener('click', function(e) {
                if (!window.isSelecting) return;
                e.preventDefault();
                e.stopPropagation();
                
                const element = e.target;
                const selectors = generateSelectors(element);
                
                // 设置选中的元素信息
                window.selectedElement = {
                    tagName: element.tagName,
                    id: element.id || '',
                    className: element.className || '',
                    textContent: element.textContent ? element.textContent.trim().substring(0, 100) : '',
                    selectors: selectors
                };
                
                // 停止选择模式
                window.isSelecting = false;
                
                // 移除高亮
                if (window.currentHighlighted) {
                    removeHighlight(window.currentHighlighted);
                }
                
                // 调用回调函数
                if (window.elementSelectCallback) {
                    window.elementSelectCallback(window.selectedElement);
                }
            });
            
            // 启用选择模式
            window.startSelection = function(callback) {
                window.isSelecting = true;
                window.elementSelectCallback = callback;
                document.body.style.cursor = 'crosshair';
            };
            
            // 停止选择模式
            window.stopSelection = function() {
                window.isSelecting = false;
                window.elementSelectCallback = null;
                document.body.style.cursor = '';
                if (window.currentHighlighted) {
                    removeHighlight(window.currentHighlighted);
                    window.currentHighlighted = null;
                }
            };
            
            console.log('数据元素选择脚本已注入');
        })();
        """
        
        await self.page.evaluate(highlight_script)
    
    async def _show_browser_guidance(self):
        """在浏览器中显示配置指导"""
        guidance_script = """
        (function() {
            // 创建指导面板
            const guidancePanel = document.createElement('div');
            guidancePanel.id = 'element-config-guidance';
            guidancePanel.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 350px;
                background: #fff;
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
                color: #333;
            `;
            
            guidancePanel.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; color: #007bff;">📋 数据元素配置</h3>
                    <button id="close-guidance" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">关闭</button>
                </div>
                
                <div id="guidance-content">
                    <div style="background: #e7f3ff; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                        <p style="margin: 0 0 10px 0;"><strong>🎯 配置说明：</strong></p>
                        <ol style="margin: 0; padding-left: 20px;">
                            <li>请先手动登录到网站</li>
                            <li>导航到包含股票数据的页面</li>
                            <li>确保数据表格已完全加载</li>
                            <li>准备好后点击"开始配置"按钮</li>
                        </ol>
                    </div>
                    
                    <div style="text-align: center;">
                        <button id="start-config-btn" style="background: #28a745; color: white; border: none; border-radius: 6px; padding: 12px 24px; font-size: 16px; cursor: pointer; font-weight: bold;">
                            🚀 开始配置
                        </button>
                    </div>
                </div>
                
                <div id="step-info" style="display: none; background: #fff3cd; padding: 15px; border-radius: 6px; margin-top: 15px;">
                    <p id="step-text" style="margin: 0; font-weight: bold;"></p>
                    <p id="step-hint" style="margin: 5px 0 0 0; color: #666;"></p>
                </div>
            `;
            
            document.body.appendChild(guidancePanel);
            
            // 绑定事件
            document.getElementById('close-guidance').onclick = function() {
                guidancePanel.style.display = 'none';
            };
            
            document.getElementById('start-config-btn').onclick = function() {
                window.configReady = true;
                document.getElementById('guidance-content').style.display = 'none';
                document.getElementById('step-info').style.display = 'block';
                document.getElementById('step-text').textContent = '准备开始配置...';
                document.getElementById('step-hint').textContent = '请等待系统准备配置环境';
            };
            
            // 更新步骤信息的函数
            window.updateStepInfo = function(stepText, hintText) {
                const stepInfo = document.getElementById('step-info');
                const stepTextEl = document.getElementById('step-text');
                const stepHintEl = document.getElementById('step-hint');
                
                if (stepInfo && stepTextEl && stepHintEl) {
                    stepInfo.style.display = 'block';
                    stepTextEl.textContent = stepText;
                    stepHintEl.textContent = hintText;
                }
            };
            
            // 显示成功信息的函数
            window.showConfigSuccess = function(message) {
                const guidancePanel = document.getElementById('element-config-guidance');
                if (guidancePanel) {
                    guidancePanel.innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <h3 style="color: #28a745; margin: 0 0 15px 0;">✅ 配置完成</h3>
                            <p style="margin: 0;">${message}</p>
                        </div>
                    `;
                    setTimeout(() => {
                        guidancePanel.style.display = 'none';
                    }, 3000);
                }
            };
            
            console.log('📋 数据元素配置指导面板已加载');
        })();
        """
        
        await self.page.evaluate(guidance_script)
    
    async def _wait_for_config_ready(self):
        """等待用户准备开始配置"""
        max_wait_time = 300  # 5分钟超时
        wait_interval = 0.5
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            # 检查用户是否点击了开始配置按钮
            ready = await self.page.evaluate("window.configReady")
            if ready:
                # 重置状态
                await self.page.evaluate("window.configReady = false")
                break
            
            await asyncio.sleep(wait_interval)
            elapsed_time += wait_interval
        
        if elapsed_time >= max_wait_time:
            raise Exception("等待用户开始配置超时")
    
    
    async def _start_step_by_step_selection(self):
        """开始逐步选择流程"""
        # 等待用户点击"开始配置"按钮
        await self._wait_for_config_ready()
        
        for step_index, step in enumerate(self.config_steps):
            step_text = f"步骤 {step_index + 1}/{len(self.config_steps)}: {step['description']}"
            hint_text = step['hint']
            
            # 在浏览器中更新步骤信息
            try:
                js_code = safe_js_call("window.updateStepInfo", step_text, hint_text)
                await self.page.evaluate(js_code)
            except Exception as e:
                self.logger.warning(f"更新浏览器步骤信息失败: {str(e)}")
                # 继续执行，不影响主流程
            
            # 启动选择模式
            selected_element = await self._select_single_element()
            
            if selected_element:
                # 生成最佳选择器
                best_selector = self._get_best_selector(selected_element)
                
                # 保存选择的元素
                self.selected_elements[step['name']] = best_selector
                
                # 显示成功信息
                element_text = selected_element.get('textContent', selected_element.get('tagName', 'Unknown'))
                # 限制显示长度，避免过长的文本
                if len(element_text) > 20:
                    element_text = element_text[:20] + "..."
                
                success_text = f"✓ 已选择: {element_text}"
                selector_text = f"选择器: {best_selector[:50]}{'...' if len(best_selector) > 50 else ''}"
                
                try:
                    js_code = safe_js_call("window.updateStepInfo", success_text, selector_text)
                    await self.page.evaluate(js_code)
                except Exception as e:
                    self.logger.warning(f"更新浏览器成功信息失败: {str(e)}")
                    # 继续执行，不影响主流程
                
                # 调用进度回调
                if self.progress_callback:
                    self.progress_callback(step['name'], best_selector)
                    
                # 等待一秒让用户看到反馈
                await asyncio.sleep(1)
            else:
                # 显示跳过信息
                try:
                    js_code = safe_js_call("window.updateStepInfo", "❌ 未选择元素，跳过此步骤", "")
                    await self.page.evaluate(js_code)
                except Exception as e:
                    self.logger.warning(f"更新浏览器跳过信息失败: {str(e)}")
                await asyncio.sleep(1)
        
        # 显示完成信息
        try:
            js_code = safe_js_call("window.showConfigSuccess", "🎉 数据元素选择完成！配置已保存。")
            await self.page.evaluate(js_code)
        except Exception as e:
            self.logger.warning(f"显示浏览器完成信息失败: {str(e)}")
    
    async def _select_single_element(self):
        """选择单个元素"""
        try:
            # 启动选择模式
            await self.page.evaluate("""
                window.startSelection();
                // 添加更明显的视觉提示
                document.body.style.cursor = 'crosshair';
                
                // 显示临时提示
                const tempTip = document.createElement('div');
                tempTip.id = 'temp-selection-tip';
                tempTip.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 123, 255, 0.9);
                    color: white;
                    padding: 15px 25px;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    z-index: 9999;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                `;
                tempTip.innerHTML = '🎯 请点击要配置的元素<br><small>鼠标悬停可预览高亮效果</small>';
                document.body.appendChild(tempTip);
                
                // 3秒后移除提示
                setTimeout(() => {
                    const tip = document.getElementById('temp-selection-tip');
                    if (tip) tip.remove();
                }, 3000);
            """)
            
            # 等待用户选择
            selected_element = None
            max_wait_time = 300  # 5分钟超时
            wait_interval = 0.5
            elapsed_time = 0
            
            while elapsed_time < max_wait_time:
                # 检查是否有选中的元素
                element_data = await self.page.evaluate("window.selectedElement")
                if element_data:
                    selected_element = element_data
                    # 清除选中状态
                    await self.page.evaluate("window.selectedElement = null")
                    break
                
                await asyncio.sleep(wait_interval)
                elapsed_time += wait_interval
            
            # 停止选择模式并清理界面
            await self.page.evaluate("""
                window.stopSelection();
                document.body.style.cursor = '';
                const tip = document.getElementById('temp-selection-tip');
                if (tip) tip.remove();
            """)
            
            return selected_element
            
        except Exception as e:
            self.logger.error(f"元素选择失败: {str(e)}")
            # 确保清理界面状态
            try:
                await self.page.evaluate("""
                    window.stopSelection();
                    document.body.style.cursor = '';
                    const tip = document.getElementById('temp-selection-tip');
                    if (tip) tip.remove();
                """)
            except:
                pass
            return None
    
    def _get_best_selector(self, element_data: dict) -> str:
        """获取最佳选择器"""
        selectors = element_data.get('selectors', [])
        if selectors:
            return selectors[0]['selector']  # 返回优先级最高的选择器
        
        # 如果没有生成选择器，创建一个基础的
        tag_name = element_data.get('tagName', '').lower()
        if element_data.get('id'):
            return f"#{element_data['id']}"
        elif element_data.get('className'):
            classes = element_data['className'].strip().split()
            return f".{'.'.join(classes[:2])}"  # 只使用前两个类名
        else:
            return tag_name
    
    async def _cleanup(self):
        """清理资源"""
        cleanup_errors = []
        
        try:
            if self.page:
                self.logger.info("正在关闭页面...")
                await self.page.close()
                self.page = None
        except Exception as e:
            cleanup_errors.append(f"关闭页面时出错: {str(e)}")
            
        try:
            if self.context:
                self.logger.info("正在关闭浏览器上下文...")
                await self.context.close()
                self.context = None
        except Exception as e:
            cleanup_errors.append(f"关闭浏览器上下文时出错: {str(e)}")
            
        try:
            if self.browser:
                self.logger.info("正在关闭浏览器...")
                await self.browser.close()
                self.browser = None
        except Exception as e:
            cleanup_errors.append(f"关闭浏览器时出错: {str(e)}")
            
        try:
            if self.playwright:
                self.logger.info("正在停止Playwright...")
                await self.playwright.stop()
                self.playwright = None
        except Exception as e:
            cleanup_errors.append(f"停止Playwright时出错: {str(e)}")
        
        if cleanup_errors:
            self.logger.warning(f"清理过程中出现错误: {'; '.join(cleanup_errors)}")
        else:
            self.logger.info("资源清理完成")


class ConfigurationTester:
    """配置测试器 - 验证选择器的有效性"""
    
    def __init__(self, selectors: dict):
        self.selectors = selectors
        self.logger = logging.getLogger(__name__)
        
        self.browser = None
        self.context = None
        self.page = None
        self.playwright = None
    
    async def test_selectors(self):
        """测试所有选择器"""
        try:
            # 初始化浏览器
            await self._initialize_test_browser()
            
            test_results = []
            successful_tests = 0
            
            for selector_name, selector_value in self.selectors.items():
                result = await self._test_single_selector(selector_name, selector_value)
                test_results.append(result)
                if result['success']:
                    successful_tests += 1
            
            # 返回测试结果
            if successful_tests == len(self.selectors):
                return {
                    'success': True,
                    'message': f"所有 {len(self.selectors)} 个选择器测试通过"
                }
            else:
                failed_selectors = [r['name'] for r in test_results if not r['success']]
                return {
                    'success': False,
                    'message': f"{successful_tests}/{len(self.selectors)} 个选择器测试通过\n失败的选择器: {', '.join(failed_selectors)}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"测试过程中发生错误: {str(e)}"
            }
        finally:
            await self._cleanup_test_browser()
    
    async def _initialize_test_browser(self):
        """初始化测试浏览器"""
        from playwright.async_api import async_playwright
        
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=True,  # 测试时使用无界面模式
            timeout=30000
        )
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        
        # 导航到目标页面
        from config import WEB_AUTOMATION_CONFIG
        target_url = WEB_AUTOMATION_CONFIG.get('target_url', 'https://www.topxlc.com/ddcj-yqs-xc/web/')
        await self.page.goto(target_url, wait_until='domcontentloaded')
        await self.page.wait_for_load_state('networkidle', timeout=30000)
    
    async def _test_single_selector(self, selector_name: str, selector_value: str):
        """测试单个选择器"""
        try:
            # 尝试查找元素
            elements = await self.page.query_selector_all(selector_value)
            
            if not elements:
                return {
                    'name': selector_name,
                    'success': False,
                    'error': '未找到匹配的元素'
                }
            
            # 检查元素是否可见
            visible_elements = []
            for element in elements:
                is_visible = await element.is_visible()
                if is_visible:
                    visible_elements.append(element)
            
            if not visible_elements:
                return {
                    'name': selector_name,
                    'success': False,
                    'error': f'找到 {len(elements)} 个元素，但都不可见'
                }
            
            return {
                'name': selector_name,
                'success': True,
                'error': None,
                'found_count': len(elements),
                'visible_count': len(visible_elements)
            }
            
        except Exception as e:
            return {
                'name': selector_name,
                'success': False,
                'error': f'选择器测试异常: {str(e)}'
            }
    
    async def _cleanup_test_browser(self):
        """清理测试浏览器资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            self.logger.error(f"清理测试浏览器资源时出错: {str(e)}")


def await_sync(coro):
    """改进的同步等待异步协程"""
    import asyncio
    import threading
    import concurrent.futures
    
    try:
        # 尝试获取当前事件循环
        try:
            loop = asyncio.get_running_loop()
            # 如果在运行的事件循环中，创建新线程运行
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result(timeout=300)  # 5分钟超时
        except RuntimeError:
            # 没有运行的事件循环，直接运行
            return asyncio.run(coro)
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"异步协程执行失败: {str(e)}")
        raise Exception(f"配置过程中发生错误: {str(e)}")


class HTMLDataInputDialog:
    """HTML数据输入对话框"""
    
    def __init__(self, parent_window, callback):
        """
        初始化HTML数据输入对话框
        
        Args:
            parent_window: 父窗口
            callback: 回调函数，接收HTML内容字符串
        """
        self.parent_window = parent_window
        self.callback = callback
        self.dialog_window = None
        self.html_text = None
        
    def show(self):
        """显示对话框"""
        if self.dialog_window:
            self.dialog_window.lift()
            return
        
        self.dialog_window = tk.Toplevel(self.parent_window)
        self.dialog_window.title("HTML数据输入")
        self.dialog_window.geometry("800x500")
        self.dialog_window.resizable(True, True)
        
        # 设置窗口属性
        self.dialog_window.transient(self.parent_window)
        self.dialog_window.grab_set()
        
        # 创建界面
        self._create_interface()
        
        # 居中显示
        self._center_window()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.dialog_window, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题和说明
        title_label = ttk.Label(main_frame, text="HTML数据输入", font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        desc_label = ttk.Label(main_frame, 
                              text="请粘贴包含股票数据表格的HTML内容（包括表头和数据行）：")
        desc_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        
        # HTML文本输入框
        text_frame = ttk.LabelFrame(main_frame, text="HTML内容", padding="10")
        text_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        
        # 创建带滚动条的文本框
        text_container = ttk.Frame(text_frame)
        text_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.html_text = tk.Text(text_container, height=20, width=80, wrap=tk.WORD)
        text_scrollbar = ttk.Scrollbar(text_container, orient=tk.VERTICAL, command=self.html_text.yview)
        self.html_text.configure(yscrollcommand=text_scrollbar.set)
        
        self.html_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 处理按钮
        process_btn = ttk.Button(button_frame, text="处理数据", 
                                command=self._process_data, width=15,
                                style="Accent.TButton")
        process_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 验证按钮
        validate_btn = ttk.Button(button_frame, text="验证格式", 
                                 command=self._validate_format, width=15)
        validate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空按钮
        clear_btn = ttk.Button(button_frame, text="清空", 
                              command=self._clear_content, width=10)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text="取消", 
                               command=self._close_dialog, width=10)
        cancel_btn.pack(side=tk.RIGHT)
        
        # 配置网格权重
        self.dialog_window.grid_rowconfigure(0, weight=1)
        self.dialog_window.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
        text_container.grid_rowconfigure(0, weight=1)
        text_container.grid_columnconfigure(0, weight=1)
    
    def _process_data(self):
        """处理HTML数据"""
        html_content = self.html_text.get("1.0", tk.END).strip()
        
        if not html_content:
            messagebox.showwarning("提示", "请输入HTML内容")
            return
        
        # 调用回调函数处理HTML内容
        self.callback(html_content)
        
        # 关闭对话框
        self._close_dialog()
    
    def _validate_format(self):
        """验证HTML格式"""
        html_content = self.html_text.get("1.0", tk.END).strip()
        
        if not html_content:
            messagebox.showwarning("提示", "请输入HTML内容")
            return
        
        try:
            from html_data_parser import validate_html_structure
            validation_result = validate_html_structure(html_content)
            
            if validation_result['is_valid']:
                messagebox.showinfo("验证成功", 
                                  f"HTML格式验证通过！\n\n"
                                  f"找到 {validation_result['total_columns']} 列，{validation_result['total_rows']} 行数据\n"
                                  f"目标列: {', '.join(validation_result['target_columns_found'])}")
            else:
                error_msg = validation_result.get('error', '格式验证失败')
                messagebox.showerror("验证失败", 
                                   f"HTML格式验证失败！\n\n"
                                   f"错误: {error_msg}\n\n"
                                   "请检查HTML内容是否包含正确的表格结构。")
                
        except Exception as e:
            messagebox.showerror("验证失败", f"HTML格式验证失败: {str(e)}")
    
    def _clear_content(self):
        """清空内容"""
        result = messagebox.askyesno("确认", "确定要清空所有内容吗？")
        if result:
            self.html_text.delete("1.0", tk.END)
    
    def _close_dialog(self):
        """关闭对话框"""
        if self.dialog_window:
            self.dialog_window.destroy()
            self.dialog_window = None
    
    def _center_window(self):
        """居中显示窗口"""
        self.dialog_window.update_idletasks()
        width = self.dialog_window.winfo_width()
        height = self.dialog_window.winfo_height()
        x = (self.dialog_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog_window.winfo_screenheight() // 2) - (height // 2)
        self.dialog_window.geometry(f"{width}x{height}+{x}+{y}")


def show_data_config_wizard(parent_window: tk.Tk, callback: Optional[Callable] = None):
    """显示数据配置向导的便捷函数"""
    try:
        wizard = DataElementConfigWizard(parent_window, callback)
        wizard.show_wizard()
        return wizard
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"创建数据配置向导失败: {str(e)}")
        # 显示错误信息给用户
        import tkinter.messagebox as messagebox
        messagebox.showerror("错误", f"无法启动数据配置向导: {str(e)}")
        return None


if __name__ == "__main__":
    # 测试代码
    
    # 测试safe_js_string函数
    test_strings = [
        "正常文本",
        "包含'单引号的文本",
        '包含"双引号的文本',
        "包含'单引号'和\"双引号\"的文本",
        "包含\n换行符的文本",
        "包含\\反斜杠的文本",
        "平安银行'A'股票",
        "股票代码: 000001.SZ",
        "",
        None
    ]
    
    print("测试JavaScript字符串转义函数:")
    print("=" * 50)
    
    for test_str in test_strings:
        try:
            safe_str = safe_js_string(test_str)
            print(f"原始: {repr(test_str)}")
            print(f"转义: {safe_str}")
            print(f"调用: {safe_js_call('testFunction', test_str, '额外参数')}")
            print("-" * 30)
        except Exception as e:
            print(f"错误: {test_str} -> {str(e)}")
    
    # 正常的GUI测试代码
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    def test_callback(config):
        print(f"配置完成: {config}")
        root.quit()
    
    wizard = show_data_config_wizard(root, test_callback)
    root.mainloop()