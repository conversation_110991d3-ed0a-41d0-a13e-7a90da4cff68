# OCR测试工具使用说明

## 概述

本工具包含两个OCR测试脚本，用于测试和对比不同OCR方法在24张调试图片上的识别效果，帮助找到最佳的OCR识别方案。

## 文件说明

### 1. ocr_test_simple.py - 基础OCR测试
- **功能**: 使用EasyOCR和PaddleOCR对所有图片进行基础识别测试
- **输出**: 简洁的对比报告，显示两个引擎的成功率和推荐方案
- **适用**: 快速了解两个OCR引擎的基本表现

### 2. ocr_method_comparison.py - 深度方法对比
- **功能**: 测试15种不同的图像预处理方法与两个OCR引擎的组合（共30种方案）
- **输出**: 详细的Excel报告文件和控制台排名报告
- **特色**: 支持大倍数图片放大（最高12倍），生成直观的Excel对比表格
- **适用**: 深入分析，找到最优识别方案

## 环境要求

### Python版本
- Python 3.7 或以上

### 必需依赖包
```bash
pip install opencv-python easyocr paddlepaddle paddleocr numpy pandas openpyxl
```

### 可选依赖（提升性能）
```bash
# GPU加速支持（如有NVIDIA GPU）
pip install torch torchvision
```

## 使用步骤

### 准备工作
1. 确保 `debug_images_20250724_221758` 目录存在且包含测试图片
2. 安装所需的Python依赖包

### 快速测试（推荐先运行）
```bash
python ocr_test_simple.py
```
- 运行时间：约2-5分钟
- 选择GPU加速：输入 `n`（CPU模式更稳定）
- 查看输出的推荐引擎

### 深度对比测试
```bash
python ocr_method_comparison.py
```
- 运行时间：约15-30分钟（测试30种方法组合）
- 选择GPU加速：根据硬件情况选择
- 获得最佳方法推荐和Excel详细报告

## 输出文件

### ocr_test_simple.py 输出
- `ocr_test_results.json`: 详细测试结果（JSON格式）
- 控制台输出：简洁对比报告

### ocr_method_comparison.py 输出  
- **`ocr_comparison_results.xlsx`**: Excel格式详细对比报告（主要输出）
  - **EasyOCR结果** 工作表：各方法在EasyOCR下的识别结果
  - **PaddleOCR结果** 工作表：各方法在PaddleOCR下的识别结果  
  - **对比汇总** 工作表：各方法成功率统计和排名
- `ocr_comparison_results.json`: 完整对比结果备份（JSON格式）
- 控制台输出：方法排名和推荐

## Excel报告说明

### 结果工作表格式
```
| 图片名称 | 原图直接识别 | 基础预处理 | 放大2倍 | ... | 放大12倍 | 灰度+二值化 | ... |
|----------|-------------|------------|---------|-----|----------|-------------|-----|
| 1.png    | -0.52%      | -0.52%     | -0.52%  | ... | -0.52%   | 识别失败     | ... |
| 2.png    | 识别失败     | -1.34%     | -1.34%  | ... | -1.34%   | -1.34%      | ... |
```

### 汇总工作表内容
- 各预处理方法的成功率统计
- EasyOCR和PaddleOCR的性能对比
- 结果一致性分析
- 按成功率排序的方法推荐

## 结果解读

### 成功率指标
- **100%**: 完美识别所有图片
- **80%+**: 表现优秀
- **60%+**: 表现良好
- **60%-**: 需要改进

### 推荐使用原则
1. **成功率最高**的方法优先
2. 成功率相同时，选择**速度更快**的方法
3. 考虑**结果一致性**（两个引擎结果一致的比例）

## 常见问题

### Q: 提示缺少依赖包怎么办？
A: 按照提示安装对应的包：
```bash
pip install pandas openpyxl opencv-python easyocr paddlepaddle paddleocr
```

### Q: 提示缺少pandas或openpyxl？
A: 这些是Excel输出必需的包：
```bash
pip install pandas openpyxl
```

### Q: GPU模式初始化失败？
A: 选择CPU模式（输入`n`），CPU模式同样有效

### Q: 识别效果不理想？
A: 运行深度对比测试，查看Excel报告中的详细结果，找到针对你的图片类型的最佳方法

### Q: 程序运行很慢？
A: 这是正常的，深度对比测试需要测试30种方法组合，OCR识别需要时间

### Q: Excel文件无法打开？
A: 确保安装了openpyxl包，或者使用较新版本的Excel/LibreOffice

## 集成到现有系统

### 使用测试结果
1. 打开生成的 `ocr_comparison_results.xlsx` 文件
2. 查看"对比汇总"工作表，找到成功率最高的方法
3. 在"EasyOCR结果"或"PaddleOCR结果"工作表中查看具体的识别结果
4. 选择最适合你数据的预处理方法和OCR引擎

### Excel数据分析技巧
- 使用Excel的筛选功能过滤出识别成功的结果
- 使用条件格式高亮显示识别失败的单元格
- 比较不同方法在相同图片上的识别结果
- 使用Excel的统计功能计算自定义成功率

### 代码示例
```python
# 假设Excel报告推荐使用 "放大6倍 + EasyOCR"
import cv2
import easyocr

# 初始化EasyOCR
reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)

# 预处理函数：放大6倍
def preprocess_scale_6x(image):
    height, width = image.shape[:2]
    new_width = int(width * 6.0)
    new_height = int(height * 6.0)
    return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

# 使用推荐方法
image = cv2.imread("test.png")
processed = preprocess_scale_6x(image)
results = reader.readtext(processed)

# 提取识别文本
texts = [result[1] for result in results]
print("识别结果:", texts)
```

### 根据Excel结果选择最佳方法
1. **查看对比汇总表**: 找到成功率最高的预处理方法
2. **确定最佳引擎**: 在该方法下EasyOCR和PaddleOCR哪个表现更好
3. **验证一致性**: 检查两个引擎结果一致率，选择更稳定的
4. **考虑特殊情况**: 如某些图片只有特定方法能识别，可针对性使用

## 注意事项

1. **首次运行**: EasyOCR会下载模型文件（约100MB），需要网络连接
2. **内存使用**: 深度对比测试会消耗较多内存，特别是大倍数放大时
3. **图片格式**: 支持PNG、JPG、JPEG、BMP、TIFF格式
4. **路径问题**: 确保脚本在包含测试图片目录的文件夹中运行
5. **Excel兼容性**: 生成的Excel文件兼容Excel 2007及以上版本

---

**建议使用流程**: 
1. 先运行 `ocr_test_simple.py` 了解基本情况
2. 再运行 `ocr_method_comparison.py` 获得详细的Excel对比报告
3. 分析Excel数据选择最优方案
4. 集成最佳方法到现有系统中